<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        *{
            padding: 0;
            margin: 0;
        }
        html,body{
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        #canvas-container{
            width: 100vw;
            height: 100vh;
        }

        #color-picker{
            position: absolute;
            top: 10px;
            left: 10px;
            width: 100px;
            height: 100px;
        }

        #decoration-picker{
            position: absolute;
            top: 10px;
            right: 10px;
            width: 200px;
            background: rgba(0,0,0,0.7);
            border-radius: 10px;
            padding: 15px;
            color: white;
        }

        #streamline-controls{
            position: absolute;
            top: 300px;
            right: 10px;
            width: 200px;
            background: rgba(0,0,0,0.7);
            border-radius: 10px;
            padding: 15px;
            color: white;
        }

        .decoration-item{
            display: flex;
            align-items: center;
            margin: 8px 0;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .decoration-item:hover{
            background: rgba(255,255,255,0.1);
        }

        .decoration-item.active{
            background: rgba(255,255,255,0.2);
        }

        .decoration-checkbox{
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas style="width: 100%; height: 100%;" id="canvas"></canvas>
        <div id="color-picker"></div>
        <div id="decoration-picker">
            <h3>装饰</h3>
            <div class="decoration-item">
                <input type="checkbox" class="decoration-checkbox" id="wheels">
                <span>车轮</span>
            </div>
            <div class="decoration-item">
                <input type="checkbox" class="decoration-checkbox" id="lights">
                <span>灯光</span>
            </div>
            <div class="decoration-item">
                <input type="checkbox" class="decoration-checkbox" id="interior">
                <span>内饰</span>
            </div>
            <div class="decoration-item">
                <input type="checkbox" class="decoration-checkbox" id="chrome">
                <span>铝合金</span>
            </div>
            <div class="decoration-item">
                <input type="checkbox" class="decoration-checkbox" id="glass">
                <span>玻璃</span>
            </div>
            <div class="decoration-item">
                <input type="checkbox" class="decoration-checkbox" id="bumpers">
                <span>保险杠</span>
            </div>
        </div>
    </div>
    <script type="module" src="./main.ts"></script>
</body>
</html>