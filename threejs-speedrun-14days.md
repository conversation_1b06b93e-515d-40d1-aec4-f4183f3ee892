# Three.js 超速成学习计划 - 14天掌握核心技能
- 参考一下别人的学习清单

  ![alt text](images/threejs-speedrun-14days/7a08a50ef10451d8a1ffd5e3b29d47f-e6bbe532-1d9f-44ae-9b2f-6db9553895ae.png)
> 🚀 基于HTML/CSS/JS基础的极速学习路线，每天4-5小时高强度学习

## 🎯 学习目标
- **7天内**： 掌握Three.js核心概念和基础技能
- **14天内**：能独立开发3D Web应用
- **完成2个实战项目**，具备求职/接单能力

---

## ⚡ 第一周：核心技能速成

### 第1天：环境搭建 + 基础场景 (4小时)
**上午 (2小时)：**
- Three.js简介和CDN引入
- 创建第一个场景：Scene + Camera + Renderer
- 立方体创建和渲染

**下午 (2小时)：**
- 坐标系统理解
- 物体变换：position, rotation, scale
- 渲染循环和动画帧

**必做练习：**
```javascript
// 创建旋转的彩色立方体
const cube = new THREE.Mesh(
  new THREE.BoxGeometry(),
  new THREE.MeshBasicMaterial({color: 0x00ff00})
);
```

---

### 第2天：几何体 + 材质 + 光照 (5小时)
**上午 (2.5小时)：**
- 所有基础几何体：Box, Sphere, Plane, Cylinder等
- 材质类型：Basic, Lambert, Phong, Standard
- 材质属性：color, wireframe, transparent

**下午 (2.5小时)：**
- 光照系统：AmbientLight, DirectionalLight, PointLight
- 阴影设置
- 材质与光照的配合

**必做练习：**
- 创建一个包含5种几何体的场景
- 每个物体使用不同材质和颜色
- 添加动态光照效果

---

### 第3天：相机控制 + 用户交互 (5小时)
**上午 (2.5小时)：**
- 透视相机参数详解
- OrbitControls轨道控制器
- 相机动画和路径

**下午 (2.5小时)：**
- 鼠标事件处理
- Raycaster射线检测
- 物体点击和选择
- 键盘控制

**必做练习：**
- 实现鼠标控制相机旋转
- 点击物体改变颜色
- WASD键控制物体移动

---

### 第4天：纹理贴图 + 模型加载 (5小时)
**上午 (2.5小时)：**
- TextureLoader纹理加载
- UV映射概念
- 纹理属性：repeat, offset, wrapS/T
- 多种贴图类型

**下午 (2.5小时)：**
- GLTFLoader模型加载
- 模型格式：GLTF vs GLB
- 模型动画播放
- 模型优化技巧

**必做练习：**
- 创建带纹理的地球模型
- 加载一个GLTF模型并播放动画

---

## ⚡ 第二周：实战冲刺

### 第5天：动画系统精通 (4小时)
**集中学习：**
- requestAnimationFrame深入理解
- Tween.js补间动画库
- 关键帧动画
- 物理动画模拟
- 性能优化技巧

**必做练习：**
- 创建复杂的物体运动轨迹
- 实现弹性动画效果

---

### 第6天：粒子系统 + 特效 (4小时)
**集中学习：**
- Points粒子系统
- BufferGeometry性能优化
- 粒子动画和生命周期
- 常见特效：雨雪、火焰、爆炸

**必做练习：**
- 创建星空背景
- 实现粒子爆炸效果

---

### 第7天：后处理 + 性能优化 (4小时)
**集中学习：**
- EffectComposer后处理管道
- 常用效果：Bloom, FXAA, SSAO
- 性能监控和优化
- 移动端适配

**必做练习：**
- 为场景添加辉光效果
- 实现性能监控面板

---

## 🚀 项目实战周

### 第8-10天：项目1 - 3D产品展示器 (3天)
**功能要求：**
- 产品360°旋转展示
- 多角度预设视图
- 材质/颜色切换
- 动画演示模式
- 响应式设计

**技术栈：**
- Three.js + OrbitControls
- GLTF模型加载
- GUI控制面板
- 动画系统

---

### 第11-13天：项目2 - 3D数据可视化 (3天)
**功能要求：**
- 3D柱状图/散点图
- 数据动态更新
- 交互式筛选
- 动画过渡效果
- 导出功能

**技术栈：**
- Three.js + 数据处理
- 动态几何体生成
- 用户界面集成
- 性能优化

---

### 第14天：项目整合 + 作品集 (1天)
- 代码重构和注释
- 项目部署上线
- 作品集网站搭建
- 技术总结文档

---

## 📚 极速学习资源

### 必看教程 (按优先级)
1. **Three.js官方示例** - https://threejs.org/examples/ (每天必看)
2. **Three.js Fundamentals** - 快速过一遍基础概念
3. **YouTube速成视频** - 搜索"Three.js crash course"

### 必备工具
- **VS Code** + Live Server插件
- **Chrome DevTools** - 性能调试
- **Three.js Editor** - 快速原型

### 模型资源
- **Sketchfab** - 免费GLTF模型
- **Poly Haven** - 免费纹理和HDRI
- **Mixamo** - 角色动画

---

## ⚡ 超速学习策略

### 时间分配 (每天4-5小时)
- **理论学习**: 30% (1.5小时)
- **代码实践**: 50% (2.5小时)  
- **项目应用**: 20% (1小时)

### 学习技巧
1. **代码优先** - 先写代码，再理解原理
2. **模仿改造** - 复制官方示例，然后修改
3. **问题驱动** - 遇到问题立即解决，不拖延
4. **项目导向** - 所有学习都围绕项目需求

### 每日检查清单
- [ ] 完成当日所有代码练习
- [ ] 理解核心概念，能用自己话解释
- [ ] 解决至少1个实际问题
- [ ] 为明天学习做好准备

---

## 🎯 14天后你将掌握

### 核心技能
- ✅ Three.js完整开发流程
- ✅ 3D场景搭建和渲染
- ✅ 用户交互和动画
- ✅ 性能优化技巧

### 实战经验
- ✅ 2个完整的3D项目
- ✅ 问题解决能力
- ✅ 代码调试技巧
- ✅ 项目部署经验

### 职业技能
- ✅ 具备Three.js开发岗位要求
- ✅ 能够承接3D Web项目
- ✅ 拥有完整作品集
- ✅ 掌握前沿3D Web技术

---

## 🔥 成功秘诀

1. **全身心投入** - 14天内专注学习，减少其他干扰
2. **立即实践** - 学到什么立即写代码验证
3. **不求完美** - 先实现功能，再优化代码
4. **积极求助** - 遇到问题立即查资料或求助
5. **坚持记录** - 每天记录学习心得和问题

**准备好开始这个挑战了吗？让我们开始第一天的学习！** 🚀
