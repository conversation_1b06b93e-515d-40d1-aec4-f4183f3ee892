## Three.js 速成学习路线 (预计1-3周)
这份路线的核心思想是 **“先上手，后深入”**，让你在最短的时间内看到成果，建立信心，然后逐步填补知识盲点。

这个路线分为四个阶段，让你从“Hello Cube”到能独立完成一个小型项目。

#### **第一阶段：基础入门 (目标：点亮第一个3D场景)**

**时间：** 第1-3天

**核心任务：** 在网页中渲染出一个可以旋转的立方体。

**你需要掌握的核心概念（“三剑客” + 演员）：**

1.  **场景 (Scene):** 你的3D世界，所有物体的容器，就像一个舞台。
2.  **相机 (Camera):** 你的眼睛，决定了你从哪个角度、以何种方式（透视/正交）观察场景。
3.  **渲染器 (Renderer):** 最终的“画笔”，将相机看到的内容绘制到网页的 `<canvas>` 元素上。
4.  **物体 (Mesh):** 场景中的演员，由两部分组成：
    *   **几何体 (Geometry):** 物体的形状，比如 `BoxGeometry` (立方体)、`SphereGeometry` (球体)。
    *   **材质 (Material):** 物体的外观（颜色、纹理、反光度等），比如 `MeshBasicMaterial` (不受光照影响的基础材质)。
5.  **光照 (Light):** 没有光，大部分材质都是黑的。`AmbientLight` (环境光) 和 `DirectionalLight` (平行光) 是最常用的入门光照。
6.  **渲染循环 (Render Loop):** 使用 `requestAnimationFrame` 不断地重新渲染场景，这样才能看到动画和交互。

**行动指南：**
*   找一个最基础的“Hello World”教程，不要思考太多，先完整地敲一遍代码，让立方体转起来！
*   尝试修改代码：改变立方体的颜色、大小、旋转速度。

---

#### **第二阶段：交互与进阶 (目标：让场景“活”起来)**

**时间：** 第1周内

**核心任务：** 让用户可以与你的3D场景互动。

**你需要掌握的核心概念：**

1.  **控制器 (Controls):** `OrbitControls` 是你的第一个好朋友。只需几行代码，就能让用户通过鼠标缩放、平移和旋转视角。
2.  **响应式布局:** 监听浏览器窗口的 `resize` 事件，当窗口大小变化时，同步更新相机和渲染器的尺寸，避免画面拉伸变形。
3.  **动画库 (GSAP):** 虽然 Three.js 本身可以做动画，但结合 [GSAP](https://greensock.com/gsap/) 会让动画变得极其简单和强大。你可以用它来创建平滑的相机运镜、物体移动、属性变化等。
4.  **事件与拾取 (Raycasting):** 如何知道用户点击了哪个物体？`Raycaster` (光线投射) 就是用来解决这个问题的。它能从相机发射一条“射线”，判断是否与场景中的物体相交。

**行动指南：**
*   在你第一阶段的场景里加入 `OrbitControls`。
*   加入窗口 `resize` 事件的监听。
*   尝试用 GSAP 创建一个简单的动画，比如点击按钮让相机移动到特定位置。
*   学习使用 `Raycaster`，实现鼠标悬浮到立方体上时改变其颜色的效果。

---

#### **第三阶段：视觉丰富化 (目标：告别几何体，走向真实世界)**

**时间：** 第2周

**核心任务：** 加载外部模型，并赋予它们更逼真的材质和效果。

**你需要掌握的核心概念：**

1.  **模型加载 (Model Loading):** 学习使用 `GLTFLoader` 来加载 `.gltf` 或 `.glb` 格式的模型。这是现代Web 3D的标准化格式，你可以把它理解为3D界的JPEG。
2.  **纹理 (Textures):** 学习使用 `TextureLoader` 给你的物体贴上图片，让它拥有细节，比如木纹、金属划痕等。
3.  **PBR材质 (Physically Based Rendering):** 忘掉 `MeshBasicMaterial`，开始使用 `MeshStandardMaterial` 或 `MeshPhysicalMaterial`。它们能模拟真实世界的光照物理，配合光照可以创造出非常逼真的金属、塑料、玻璃等质感。
4.  **粒子效果 (Points):** 使用 `Points` 和 `PointsMaterial` 可以用非常低的性能开销创建大量粒子，常用于制作星空、火焰、下雪等特效。

**行动指南：**
*   去 [Sketchfab](https://sketchfab.com/feed) 网站下载一个免费的 `gltf` 模型，并尝试将它加载到你的场景中。
*   给一个简单的平面（`PlaneGeometry`）添加一张地面纹理。
*   创建一个由几百个粒子组成的随机分布的星空背景。

---

#### **第四阶段：项目实战与优化 (目标：构建一个完整的迷你作品)**

**时间：** 第3周及以后

**核心任务：** 将所学知识整合，制作一个小型项目，并考虑性能问题。

**你需要掌握的核心概念：**

1.  **项目构思:** 构思一个小的、可实现的项目。例如：
    *   一个3D个人简历/作品集首页。
    *   一个简单的产品展示页面（如一双鞋、一个手机）。
    *   一个简单的地球数据可视化。
2.  **性能优化:**
    *   **几何体合并 (Merge Geometries):** 减少绘制调用 (Draw Calls)。
    *   **资源释放 (Dispose):** 当物体不再需要时，及时释放其几何体、材质、纹理占用的内存。
    *   **LOD (Level of Detail):** 根据物体离相机的远近，显示不同精度的模型。
3.  **框架集成 (可选，但强烈推荐):** 如果你熟悉 React 或 Vue，学习使用它们的 Three.js 生态库会极大提高开发效率。
    *   **React:** `@react-three/fiber` + `@react-three/drei` (这是目前最流行、最强大的组合)。
    *   **Vue:** `TresJS` 或 `TroisJS`。

**行动指南：**
*   选择一个项目构思，并动手实现它。
*   在浏览器开发者工具中打开性能监视器，观察你的项目在运行时的帧率和内存占用。
*   （可选）尝试用 `@react-three/fiber` 重构你之前的某个小练习，感受声明式写法的魅力。

---


### 速成心态建议

*   **多抄多改:** 初期不要害怕复制粘贴示例代码。关键是先让它跑起来，然后再逐行修改参数，观察变化，以此来理解每一行代码的作用。
*   **善用 `console.log`:** 它是你最好的调试工具。不确定一个对象里有什么属性？打印出来看看。
*   **先视觉，后数学:** Three.js 背后是线性代数，但你不需要成为数学家才能开始。先用它创造出酷炫的视觉效果，遇到问题再去反向学习背后的数学原理，这样更有动力。
*   **保持乐趣:** Three.js 的学习过程充满了“哇！”的时刻。享受每一个从代码变成惊艳画面的瞬间，这是你坚持下去的最大动力。



## Three.js 超速成学习计划 - 14天掌握核心技能
- 参考一下别人的学习清单

  ![alt text](images/threejs-speedrun-14days/7a08a50ef10451d8a1ffd5e3b29d47f-e6bbe532-1d9f-44ae-9b2f-6db9553895ae.png)
> 🚀 基于HTML/CSS/JS基础的极速学习路线，每天4-5小时高强度学习

## 🎯 学习目标
- **7天内**：掌握Three.js核心概念和基础技能
- **14天内**：能独立开发3D Web应用
- **完成2个实战项目**，具备求职/接单能力

---

## ⚡ 第一周：核心技能速成

### 第1天：环境搭建 + 基础场景 (4小时)
**上午 (2小时)：**
- Three.js简介和CDN引入
- 创建第一个场景：Scene + Camera + Renderer
- 立方体创建和渲染

**下午 (2小时)：**
- 坐标系统理解
- 物体变换：position, rotation, scale
- 渲染循环和动画帧

**必做练习：**
```javascript
// 创建旋转的彩色立方体
const cube = new THREE.Mesh(
  new THREE.BoxGeometry(),
  new THREE.MeshBasicMaterial({color: 0x00ff00})
);
```

---

### 第2天：几何体 + 材质 + 光照 (5小时)
**上午 (2.5小时)：**
- 所有基础几何体：Box, Sphere, Plane, Cylinder等
- 材质类型：Basic, Lambert, Phong, Standard
- 材质属性：color, wireframe, transparent

**下午 (2.5小时)：**
- 光照系统：AmbientLight, DirectionalLight, PointLight
- 阴影设置
- 材质与光照的配合

**必做练习：**
- 创建一个包含5种几何体的场景
- 每个物体使用不同材质和颜色
- 添加动态光照效果

---

### 第3天：相机控制 + 用户交互 (5小时)
**上午 (2.5小时)：**
- 透视相机参数详解
- OrbitControls轨道控制器
- 相机动画和路径

**下午 (2.5小时)：**
- 鼠标事件处理
- Raycaster射线检测
- 物体点击和选择
- 键盘控制

**必做练习：**
- 实现鼠标控制相机旋转
- 点击物体改变颜色
- WASD键控制物体移动

---

### 第4天：纹理贴图 + 模型加载 (5小时)
**上午 (2.5小时)：**
- TextureLoader纹理加载
- UV映射概念
- 纹理属性：repeat, offset, wrapS/T
- 多种贴图类型

**下午 (2.5小时)：**
- GLTFLoader模型加载
- 模型格式：GLTF vs GLB
- 模型动画播放
- 模型优化技巧

**必做练习：**
- 创建带纹理的地球模型
- 加载一个GLTF模型并播放动画

---

## ⚡ 第二周：实战冲刺

### 第5天：动画系统精通 (4小时)
**集中学习：**
- requestAnimationFrame深入理解
- Tween.js补间动画库
- 关键帧动画
- 物理动画模拟
- 性能优化技巧

**必做练习：**
- 创建复杂的物体运动轨迹
- 实现弹性动画效果

---

### 第6天：粒子系统 + 特效 (4小时)
**集中学习：**
- Points粒子系统
- BufferGeometry性能优化
- 粒子动画和生命周期
- 常见特效：雨雪、火焰、爆炸

**必做练习：**
- 创建星空背景
- 实现粒子爆炸效果

---

### 第7天：后处理 + 性能优化 (4小时)
**集中学习：**
- EffectComposer后处理管道
- 常用效果：Bloom, FXAA, SSAO
- 性能监控和优化
- 移动端适配

**必做练习：**
- 为场景添加辉光效果
- 实现性能监控面板

---

## 🚀 项目实战周

### 第8-10天：项目1 - 3D产品展示器 (3天)
**功能要求：**
- 产品360°旋转展示
- 多角度预设视图
- 材质/颜色切换
- 动画演示模式
- 响应式设计

**技术栈：**
- Three.js + OrbitControls
- GLTF模型加载
- GUI控制面板
- 动画系统

---

### 第11-13天：项目2 - 3D数据可视化 (3天)
**功能要求：**
- 3D柱状图/散点图
- 数据动态更新
- 交互式筛选
- 动画过渡效果
- 导出功能

**技术栈：**
- Three.js + 数据处理
- 动态几何体生成
- 用户界面集成
- 性能优化

---

### 第14天：项目整合 + 作品集 (1天)
- 代码重构和注释
- 项目部署上线
- 作品集网站搭建
- 技术总结文档

---



-  超速学习策略

### 时间分配 (每天4-5小时)
- **理论学习**: 30% (1.5小时)
- **代码实践**: 50% (2.5小时)  
- **项目应用**: 20% (1小时)

### 学习技巧
1. **代码优先** - 先写代码，再理解原理
2. **模仿改造** - 复制官方示例，然后修改
3. **问题驱动** - 遇到问题立即解决，不拖延
4. **项目导向** - 所有学习都围绕项目需求

### 每日检查清单
- [ ] 完成当日所有代码练习
- [ ] 理解核心概念，能用自己话解释
- [ ] 解决至少1个实际问题
- [ ] 为明天学习做好准备

---

-  🎯 14天后你将掌握

### 核心技能
- ✅ Three.js完整开发流程
- ✅ 3D场景搭建和渲染
- ✅ 用户交互和动画
- ✅ 性能优化技巧

### 实战经验
- ✅ 2个完整的3D项目
- ✅ 问题解决能力
- ✅ 代码调试技巧
- ✅ 项目部署经验

### 职业技能
- ✅ 具备Three.js开发岗位要求
- ✅ 能够承接3D Web项目
- ✅ 拥有完整作品集
- ✅ 掌握前沿3D Web技术


## 长久路线
- 学习目标
  - 掌握 Three.js 核心概念和 API
  - 能够创建交互式 3D 场景
  - 完成 2-3 个完整的 3D 项目
  - 具备独立开发 3D Web 应用的能力

## 🎯 学习路线概览

### 阶段一：核心概念掌握 (第1-4天)
### 阶段二：基础实践应用 (第5-12天)  
### 阶段三：进阶技能提升 (第13-24天)
### 阶段四：项目实战演练 (第25-35天)

---

## 📅 详细学习计划

### 第1天：环境搭建与第一个场景
**学习目标：** 搭建开发环境，理解 Three.js 基本结构

**学习内容：**
- Three.js 简介和应用场景
- 开发环境搭建（CDN vs NPM）
- 创建第一个 3D 场景
- Scene、Camera、Renderer 三大核心

**实践项目：**
- 创建一个旋转的立方体
- 理解坐标系统

**参考资源：**
- [Three.js 官方入门教程](https://threejs.org/docs/#manual/introduction/Creating-a-scene)
- [WebGL 基础概念](https://webglfundamentals.org/)

---

### 第2天：几何体与材质
**学习目标：** 掌握各种几何体和材质的使用

**学习内容：**
- 基础几何体：BoxGeometry, SphereGeometry, PlaneGeometry 等
- 材质类型：MeshBasicMaterial, MeshLambertMaterial, MeshPhongMaterial
- 几何体变换：位置、旋转、缩放

**实践项目：**
- 创建多个不同几何体的场景
- 尝试不同材质效果

---

### 第3天：相机与控制器
**学习目标：** 理解相机系统和用户交互

**学习内容：**
- 透视相机 vs 正交相机
- 相机参数：fov, aspect, near, far
- OrbitControls 轨道控制器
- 相机动画

**实践项目：**
- 实现鼠标控制相机旋转
- 创建相机动画效果

---

### 第4天：光照系统
**学习目标：** 掌握各种光源的使用

**学习内容：**
- 环境光 AmbientLight
- 方向光 DirectionalLight  
- 点光源 PointLight
- 聚光灯 SpotLight
- 光照与阴影

**实践项目：**
- 创建真实感光照场景
- 实现动态光照效果

---

### 第5-6天：纹理与贴图
**学习目标：** 学会使用纹理增强视觉效果

**学习内容：**
- 纹理加载器 TextureLoader
- UV 映射概念
- 纹理属性：repeat, offset, rotation
- 法线贴图、置换贴图

**实践项目：**
- 为几何体添加纹理
- 创建地球模型

---

### 第7-8天：模型加载
**学习目标：** 加载和使用外部 3D 模型

**学习内容：**
- GLTF/GLB 格式
- GLTFLoader 使用
- 模型优化和压缩
- 模型动画

**实践项目：**
- 加载并展示 3D 模型
- 实现模型查看器

---

### 第9-10天：动画基础
**学习目标：** 创建各种动画效果

**学习内容：**
- requestAnimationFrame 循环
- Tween.js 补间动画
- 关键帧动画
- 骨骼动画基础

**实践项目：**
- 创建物体运动动画
- 实现相机路径动画

---

### 第11-12天：交互事件
**学习目标：** 实现用户交互功能

**学习内容：**
- 鼠标事件处理
- Raycaster 射线检测
- 物体选择和高亮
- 拖拽功能实现

**实践项目：**
- 可点击的 3D 物体
- 拖拽移动物体

---

### 第13-15天：高级材质与着色器
**学习目标：** 深入理解材质系统

**学习内容：**
- PBR 材质 MeshStandardMaterial
- 自定义着色器基础
- ShaderMaterial 使用
- 顶点着色器和片段着色器

**实践项目：**
- 创建自定义材质效果
- 实现水面波动效果

---

### 第16-18天：粒子系统
**学习目标：** 创建粒子特效

**学习内容：**
- Points 粒子系统
- BufferGeometry 优化
- 粒子动画
- GPU 粒子系统

**实践项目：**
- 星空背景效果
- 火焰粒子系统

---

### 第19-21天：后处理效果
**学习目标：** 添加后处理特效

**学习内容：**
- EffectComposer 合成器
- 常用后处理效果
- 自定义后处理通道
- 性能优化

**实践项目：**
- 添加辉光效果
- 景深模糊效果

---

### 第22-24天：性能优化
**学习目标：** 优化 3D 应用性能

**学习内容：**
- 几何体合并
- 实例化渲染
- LOD 细节层次
- 纹理优化
- 渲染优化技巧

**实践项目：**
- 优化大场景渲染
- 性能监控工具使用

---

### 第25-28天：综合项目1 - 3D 产品展示
**项目目标：** 创建一个完整的 3D 产品展示应用

**功能要求：**
- 产品 360° 展示
- 材质切换功能
- 动画演示
- 响应式设计

---

### 第29-32天：综合项目2 - 3D 数据可视化
**项目目标：** 使用 Three.js 创建数据可视化应用

**功能要求：**
- 3D 图表展示
- 交互式数据探索
- 动态数据更新
- 用户界面集成

---

### 第33-35天：作品集整理
**目标：** 完善项目，准备作品集

**任务：**
- 代码重构和优化
- 添加项目文档
- 部署到线上平台
- 准备技术分享


### ✅ 学习检查清单

### 基础概念
- [ ] 理解 Scene、Camera、Renderer 关系
- [ ] 掌握基本几何体创建
- [ ] 熟悉材质系统
- [ ] 会使用光照系统

### 实践技能
- [ ] 能加载外部模型
- [ ] 会创建动画效果
- [ ] 掌握用户交互
- [ ] 理解性能优化

### 项目经验
- [ ] 完成产品展示项目
- [ ] 完成数据可视化项目
- [ ] 具备独立开发能力

---

### 🎯 学习建议

1. **每天坚持 2-3 小时学习**
2. **理论学习后立即实践**
3. **多看官方示例代码**
4. **遇到问题及时查阅文档**
5. **加入社区获取帮助**
6. **记录学习笔记和心得**
