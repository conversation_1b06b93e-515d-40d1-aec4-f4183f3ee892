# threejs学习
## 资源
- 文档、博客
  - [Three.js教程](http://www.webgl3d.cn/Three.js/)
  - [Three.js 编辑器](https://threejs.org/editor/)
  - [发布 ·mrdoob/three.js --- Releases · mrdoob/three.js](https://github.com/mrdoob/three.js/releases)
  - [Three.js应用的结构 | Discover three.js](https://discoverthreejs.com/zh/book/first-steps/app-structure/)
  - [threejs-tutorial/04 添加一些自适应.md at main · puxiao/threejs-tutorial](https://github.com/puxiao/threejs-tutorial/blob/main/04%20%E6%B7%BB%E5%8A%A0%E4%B8%80%E4%BA%9B%E8%87%AA%E9%80%82%E5%BA%94.md)
  - [Three.js零基础入门教程(2023版本)threejs文件包下载和目录简介 在正式学习Three.js之前，先做一 - 掘金](https://juejin.cn/post/7197324377943081019)
  - [基础 - three.js manual](https://threejs.org/manual/#zh/fundamentals)
  - [three.js examples](https://threejs.org/examples/#webgl_animation_keyframes)
- 项目
  - [(50 封私信 / 14 条消息) Vue+Three.js，新手demo - 知乎](https://zhuanlan.zhihu.com/p/333615381)
  - [zhangbo126/threejs-3dmodel-edit: 基于three.js开发的3D模型可视化编辑器 包含模型加载，模型文件导入导出，模型背景图，全景图，模型动画，模型灯光，模型定位，辅助线，模型辉光，模型拖拽，模型拆解， 模型材质等可视化操作编辑系统，模型编辑数据保存预览和代码嵌入 前端技术:Vue3+Typescript+Pinia+Threejs](https://github.com/zhangbo126/threejs-3dmodel-edit)
- 社区
  - [discord](https://discord.com/channels/685241246557667386/1020015693535653948)
#### **入门必看 (首选)**

1.  **Three.js Journey (布鲁诺·西蒙的课程):**
    *   **链接:** [https://threejs-journey.com/](https://threejs-journey.com/)
    *   **评价:** **这是目前学习Three.js最好的课程，没有之一！** 从零基础讲起，风趣幽默，内容全面且深入。虽然是付费课程，但绝对物超所值。如果你预算充足，直接买它，可以帮你节省大量自学摸索的时间。

2.  **Three.js 官方文档与示例:**
    *   **链接:** [https://threejs.org/](https://threejs.org/)
    *   **评价:** 免费且权威。文档是你的字典，示例是你最好的灵感和代码参考来源。遇到任何API不解，先查文档；想实现某个效果，先去示例里找找有没有类似的。

#### **中文优质资源**

1.  **郭隆邦的博客 (老郭):**
    *   **链接:** [http://www.yanhuangxueyuan.com/](http://www.yanhuangxueyuan.com/)
    *   **评价:** 非常系统化的中文Three.js教程网站，从基础到案例都有，讲解细致，适合国内开发者。

#### **灵感、模型与素材**

1.  **Awwwards:**
    *   **链接:** [https://www.awwwards.com/](https://www.awwwards.com/)
    *   **评价:** 顶级网页设计灵感来源。搜索 "Three.js" 或 "WebGL"，你会看到无数惊艳的商业案例，能极大拓宽你的视野。
2.  **Sketchfab:**
    *   **链接:** [https://sketchfab.com/](https://sketchfab.com/)
    *   **评价:** 全球最大的3D模型分享平台。海量免费、高质量的 `gltf` 模型可供下载，是你练习模型加载的不二之选。
3.  **Poliigon / Poly Haven:**
    *   **链接:** [https://www.poliigon.com/](https://www.poliigon.com/) (部分免费), [https://polyhaven.com/](https://polyhaven.com/) (完全免费)
    *   **评价:** 高质量PBR纹理和HDRI环境贴图下载站，能让你的渲染效果提升一个档次。

#### **进阶与生态**

1.  **React Three Fiber (R3F) 文档:**
    *   **链接:** [https://docs.pmnd.rs/react-three-fiber/getting-started/introduction](https://docs.pmnd.rs/react-three-fiber/getting-started/introduction)
    *   **评价:** 如果你用React，R3F是必学的。它让你可以用声明式的组件化方式来构建Three.js场景，代码更优雅，生态也极其丰富（drei库提供了大量开箱即用的组件）。
2.  **GSAP (GreenSock Animation Platform):**
    *   **链接:** [https://greensock.com/gsap/](https://greensock.com/gsap/)
    *   **评价:** Web动画的瑞士军刀。控制相机、物体动画的首选工具，性能好，API友好。
3.  **The Book of Shaders:**
    *   **链接:** [https://thebookofshaders.com/](https://thebookofshaders.com/)
    *   **评价:** 当你想深入底层，编写自己的着色器（GLSL）来实现独特视觉效果时，这是一本必读的免费入门神书。



### 工具资源
- [Blender](https://www.blender.org/) - 3D 建模软件
- [Sketchfab](https://sketchfab.com/) - 3D 模型库
- [Poly Haven](https://polyhaven.com/) - 免费 HDRI 和纹理

### 社区支持
- [Three.js Discord](https://discord.gg/56GBJwAnUS)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/three.js)
- [Reddit r/threejs](https://www.reddit.com/r/threejs/)
## 学习方法
- 直接上手项目，看b站视频课程
- 视频顺序
  - [建立一个令人兴奋的 3D 作品集网站（Three.js初学者教程）\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV1FA4m1c7Q8/?vd_source=a02025349c3063d9c9143f9bd7489fdf)
  - [快速上手Three.js - 浏览器中的3D渲染引擎 [threejs]\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV1zU4y1L7Go/?vd_source=a02025349c3063d9c9143f9bd7489fdf)
  -  [一小时，带你入门 ThreeJS，实现一个 3D 展车的功能\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV139CGYqEa4/?vd_source=a02025349c3063d9c9143f9bd7489fdf)
  - [30分钟带你掌握WebGL和Threejs\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV1LB4y197Ew/?vd_source=a02025349c3063d9c9143f9bd7489fdf)
  - [three.js全网最全最新入门课程（2024年6月更新）【搞定前端前沿技术】\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV1Gg411X7FY/?vd_source=a02025349c3063d9c9143f9bd7489fdf)
  - [three.js从入门到精通\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV175411Q7U2/?spm_id_from=333.999.0.0&vd_source=fc98bc82ca25234b3a3030baea035443)
 
## note