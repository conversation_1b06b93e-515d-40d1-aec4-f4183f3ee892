{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 3348, "max": [0.08267690986394882, -0.1303337961435318, 0.08553911000490189], "min": [-0.08149939775466919, -0.24563199281692505, -0.08780033886432648], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 40176, "componentType": 5126, "count": 3348, "max": [1.0, 1.0, 0.9999337196350098], "min": [-1.0, -0.9999879598617554, -0.9999237060546875], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 3348, "max": [0.9986540079116821, 0.9811950325965881], "min": [0.00946700107306242, 0.008463025093078613], "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 8385, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 80352, "componentType": 5126, "count": 3354, "max": [0.0820513665676117, -0.13040995597839355, 0.08553911000490189], "min": [-0.08212496340274811, -0.24570833146572113, -0.08780033886432648], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 120600, "componentType": 5126, "count": 3354, "max": [1.0, 1.0, 0.9999330639839172], "min": [-1.0, -0.999988317489624, -0.9999252557754517], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 26784, "componentType": 5126, "count": 3354, "max": [0.9986540079116821, 0.9811950325965881], "min": [0.00946700107306242, 0.008463025093078613], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 33540, "componentType": 5125, "count": 8385, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 160848, "componentType": 5126, "count": 2169, "max": [0.10849350690841675, -0.07825088500976562, 0.18530897796154022], "min": [-0.022405683994293213, -0.2301785945892334, -0.18706569075584412], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 186876, "componentType": 5126, "count": 2169, "max": [0.9999992847442627, 0.9999901652336121, 0.9999951720237732], "min": [-0.9999988079071045, -0.9999671578407288, -0.9999977350234985], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 53616, "componentType": 5126, "count": 2169, "max": [0.6862269639968872, 0.9968379735946655], "min": [0.011227000504732132, 0.3365020155906677], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 67080, "componentType": 5125, "count": 7536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 212904, "componentType": 5126, "count": 4, "max": [0.10956686735153198, -0.14926433563232422, 0.07788792252540588], "min": [0.10956686735153198, -0.17840206623077393, -0.05825132131576538], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 212952, "componentType": 5126, "count": 4, "max": [1.0, 0.0, 0.0], "min": [1.0, -2.1709264785840787e-07, 0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 70968, "componentType": 5126, "count": 4, "max": [0.9512761235237122, 0.9844698309898376], "min": [0.07206946611404419, 0.8015416860580444], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 97224, "componentType": 5125, "count": 6, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 213000, "componentType": 5126, "count": 2169, "max": [0.0345156192779541, -0.07786178588867188, 0.18732774257659912], "min": [-0.09638363122940063, -0.23018240928649902, -0.18508127331733704], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 239028, "componentType": 5126, "count": 2169, "max": [0.9999986886978149, 0.99997878074646, 0.999997079372406], "min": [-0.9999993443489075, -0.9999445080757141, -0.9999918937683105], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 71000, "componentType": 5126, "count": 2169, "max": [0.6862269639968872, 0.9968379735946655], "min": [0.011227000504732132, 0.3365020155906677], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 97248, "componentType": 5125, "count": 7536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 265056, "componentType": 5126, "count": 4, "max": [-0.09706705808639526, -0.15112245082855225, 0.07747805118560791], "min": [-0.09706705808639526, -0.18007969856262207, -0.058340251445770264], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 265104, "componentType": 5126, "count": 4, "max": [-1.0, 2.7074469244325883e-07, 0.0], "min": [-1.0, 0.0, 0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 88352, "componentType": 5126, "count": 4, "max": [0.9512761235237122, 0.9844698309898376], "min": [0.07206946611404419, 0.8015416860580444], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 127392, "componentType": 5125, "count": 6, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 265152, "componentType": 5126, "count": 2701, "max": [0.9157396554946899, 0.0031047766096889973, 0.29201748967170715], "min": [-0.026002712547779083, -0.14126229286193848, -0.021361177787184715], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 297564, "componentType": 5126, "count": 2701, "max": [0.9977980256080627, 0.9990917444229126, 0.9946697950363159], "min": [-0.9979857802391052, -0.9999909400939941, -0.9960131049156189], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 88384, "componentType": 5126, "count": 2701, "max": [0.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 127416, "componentType": 5125, "count": 15246, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 329976, "componentType": 5126, "count": 2568, "max": [0.9630840420722961, 0.05993372201919556, 0.027417266741394997], "min": [-0.022737503051757812, -0.04134494066238403, -0.11620590090751648], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 360792, "componentType": 5126, "count": 2568, "max": [0.9989019632339478, 0.9998546242713928, 0.9991441369056702], "min": [-0.9987981915473938, -0.9999392628669739, -0.9998072385787964], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 109992, "componentType": 5126, "count": 2568, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 188400, "componentType": 5125, "count": 14904, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 391608, "componentType": 5126, "count": 649, "max": [0.03543984889984131, 0.015268215909600258, 0.030618222430348396], "min": [-0.034246087074279785, 0.010330273769795895, -0.03603984788060188], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 399396, "componentType": 5126, "count": 649, "max": [0.9883182048797607, 0.015748556703329086, 0.999988853931427], "min": [-0.9879100918769836, -0.9999564290046692, -0.9999856948852539], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 130536, "componentType": 5126, "count": 649, "max": [0.7020909786224365, 0.5828839540481567], "min": [0.011533000506460667, -5.746039867401123], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 248016, "componentType": 5125, "count": 3432, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 407184, "componentType": 5126, "count": 3771, "max": [0.21226730942726135, 0.13265925645828247, 0.2039749026298523], "min": [-0.21111446619033813, -0.0013454725267365575, -0.19982682168483734], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 452436, "componentType": 5126, "count": 3771, "max": [0.9996916651725769, 0.999988853931427, 0.9996489882469177], "min": [-0.9994943737983704, -0.999988317489624, -0.9999256134033203], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 135728, "componentType": 5126, "count": 3771, "max": [4.007717132568359, 5.117877006530762], "min": [-3.950439929962158, -3.946197986602783], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 261744, "componentType": 5125, "count": 20880, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 497688, "componentType": 5126, "count": 427, "max": [0.134079247713089, 0.11206617951393127, 0.06550519168376923], "min": [-0.13261771202087402, 0.016507012769579887, -0.11071578413248062], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 502812, "componentType": 5126, "count": 427, "max": [0.9991176724433899, 0.9919430017471313, 0.9994754195213318], "min": [-0.9995414614677429, -0.992428183555603, -0.9817118048667908], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 165896, "componentType": 5126, "count": 427, "max": [3.2040209770202637, -3.9729819297790527], "min": [-3.9751040935516357, -4.112270832061768], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 345264, "componentType": 5125, "count": 2160, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 507936, "componentType": 5126, "count": 6, "max": [1.0430831909179688, -2.269113540649414, -0.129683256149292], "min": [-1.0430834293365479, -2.2691848278045654, -0.12972459197044373], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 508008, "componentType": 5126, "count": 6, "max": [0.9906826615333557, -0.031823061406612396, -0.1058383360505104], "min": [-0.9906821846961975, -0.40297555923461914, -0.2408079206943512], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 169312, "componentType": 5126, "count": 6, "max": [0.9857905507087708, 0.4600437879562378], "min": [0.893082320690155, 0.3825840353965759], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 169360, "componentType": 5126, "count": 6, "max": [0.980252206325531, 3.15496826171875], "min": [0.8891516327857971, 3.0355169773101807], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 353904, "componentType": 5125, "count": 6, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 508080, "componentType": 5126, "count": 530, "max": [0.9588504433631897, 2.619741201400757, 0.6465465426445007], "min": [-0.9583275318145752, -1.727310061454773, -0.5518655180931091], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 514440, "componentType": 5126, "count": 530, "max": [1.0, 0.9944482445716858, 0.9999966621398926], "min": [-1.0, -0.9999950528144836, -0.9999441504478455], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 169408, "componentType": 5126, "count": 530, "max": [748.1475830078125, 1730.5], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 353928, "componentType": 5125, "count": 1884, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 520800, "componentType": 5126, "count": 473, "max": [1.076836347579956, -1.5225664377212524, 0.5236510038375854], "min": [-1.0763142108917236, -2.5786032676696777, -0.4948921799659729], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 526476, "componentType": 5126, "count": 473, "max": [0.9948599338531494, 0.0031425205525010824, 0.8761259913444519], "min": [-0.9948649406433105, -0.9999979734420776, -0.9999655485153198], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 173648, "componentType": 5126, "count": 473, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 361464, "componentType": 5125, "count": 1692, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 532152, "componentType": 5126, "count": 28401, "max": [1.0918699502944946, 2.456130027770996, 0.787056028842926], "min": [-1.0913499593734741, -2.2339401245117188, -0.5960140228271484], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 872964, "componentType": 5126, "count": 28401, "max": [0.999999463558197, 0.9999982714653015, 0.9999449849128723], "min": [-0.9999998211860657, -1.0, -0.9999904036521912], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 177432, "componentType": 5126, "count": 28401, "max": [0.9996829628944397, 0.8782520294189453], "min": [0.0003169999981764704, 0.00031697750091552734], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 368232, "componentType": 5125, "count": 141864, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1213776, "componentType": 5126, "count": 6434, "max": [1.0699100494384766, 1.1833699941635132, 0.7500729560852051], "min": [-1.069390058517456, -1.3745100498199463, -0.49135899543762207], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1290984, "componentType": 5126, "count": 6434, "max": [1.0, 0.999912440776825, 0.9999433755874634], "min": [-1.0, -0.9997636675834656, -0.6420078873634338], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 404640, "componentType": 5126, "count": 6434, "max": [0.9609590172767639, 0.9998610615730286], "min": [0.00013899999612476677, 0.00013899803161621094], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 935688, "componentType": 5125, "count": 31848, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1368192, "componentType": 5126, "count": 3550, "max": [0.9306149482727051, -1.945552110671997, 0.3023323118686676], "min": [-0.93009352684021, -2.406123399734497, 0.11316713690757751], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1410792, "componentType": 5126, "count": 3550, "max": [0.9999994039535522, 0.06441295892000198, 0.9999918341636658], "min": [-0.9999995231628418, -0.9877839088439941, -0.9999969005584717], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 456112, "componentType": 5126, "count": 3550, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1063080, "componentType": 5125, "count": 19044, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1453392, "componentType": 5126, "count": 3076, "max": [0.9390408992767334, 2.4654786586761475, 0.13615557551383972], "min": [-0.9385184049606323, 1.9275721311569214, -0.10196562111377716], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1490304, "componentType": 5126, "count": 3076, "max": [0.9977208375930786, 0.9994937181472778, 0.9998312592506409], "min": [-0.9977177381515503, -0.9714499711990356, -0.999995768070221], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 484512, "componentType": 5126, "count": 3076, "max": [839.255615234375, 2588.48193359375], "min": [467.40850830078125, 2092.125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1139256, "componentType": 5125, "count": 16356, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1527216, "componentType": 5126, "count": 583, "max": [0.3765419125556946, -2.0792670249938965, 0.5070122480392456], "min": [-0.37601831555366516, -2.1823928356170654, 0.4874771535396576], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1534212, "componentType": 5126, "count": 583, "max": [0.9867205619812012, 0.03442146256566048, 0.9997808337211609], "min": [-0.986739993095398, -0.9994136095046997, -0.998879075050354], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 509120, "componentType": 5126, "count": 583, "max": [13.828869819641113, -18.655519485473633], "min": [7.56451416015625, -19.28519058227539], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1204680, "componentType": 5125, "count": 3024, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1541208, "componentType": 5126, "count": 3078, "max": [0.9171153903007507, -2.186171770095825, 0.2870815694332123], "min": [-0.916748583316803, -2.387380599975586, 0.11969190090894699], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1578144, "componentType": 5126, "count": 3078, "max": [0.9997726082801819, 0.40835288166999817, 0.9999950528144836], "min": [-0.9999469518661499, -0.9991441965103149, -0.9999954700469971], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 513784, "componentType": 5126, "count": 3078, "max": [0.33329400420188904, 0.8129809498786926], "min": [-0.16671599447727203, -5.823192119598389], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1216776, "componentType": 5125, "count": 17604, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1615080, "componentType": 5126, "count": 282, "max": [0.9187616109848022, -2.0646770000457764, 0.24232111871242523], "min": [-0.9182398915290833, -2.193794012069702, 0.2057127058506012], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1618464, "componentType": 5126, "count": 282, "max": [0.928647518157959, -0.154907688498497, 0.475520521402359], "min": [-0.9286080002784729, -0.22598792612552643, 0.3232370615005493], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 538408, "componentType": 5126, "count": 282, "max": [4.527966022491455, -1.5902729034423828], "min": [-0.6602969765663147, -5.911847114562988], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 540664, "componentType": 5126, "count": 282, "max": [4.527966022491455, -1.5902729034423828], "min": [-0.6602969765663147, -5.911847114562988], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1287192, "componentType": 5125, "count": 1488, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1621848, "componentType": 5126, "count": 1162, "max": [0.9168273210525513, -2.1890206336975098, 0.28698965907096863], "min": [-0.9163057208061218, -2.39909291267395, 0.11850877851247787], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1635792, "componentType": 5126, "count": 1162, "max": [0.9999131560325623, 0.1596960574388504, 0.9998823404312134], "min": [-0.9999138712882996, -0.9961560964584351, -0.9999911785125732], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 542920, "componentType": 5126, "count": 1162, "max": [0.4296904504299164, 0.886360764503479], "min": [0.014246433973312378, 0.4572017192840576], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 552216, "componentType": 5126, "count": 1162, "max": [0.4296904504299164, 0.886360764503479], "min": [0.014246433973312378, 0.4572017192840576], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1293144, "componentType": 5125, "count": 6432, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1649736, "componentType": 5126, "count": 302, "max": [0.8224537968635559, -2.3059985637664795, 0.2216726690530777], "min": [-0.8219316005706787, -2.3722286224365234, 0.2120615392923355], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1653360, "componentType": 5126, "count": 302, "max": [0.9401693344116211, 0.3192727565765381, 0.9997594356536865], "min": [-0.9399200677871704, -0.9512227773666382, -0.9924485683441162], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 561512, "componentType": 5126, "count": 302, "max": [0.2207077592611313, 0.7382915019989014], "min": [0.011847257614135742, 0.7095489501953125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1318872, "componentType": 5125, "count": 1632, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1656984, "componentType": 5126, "count": 787, "max": [0.895634114742279, 2.2257673740386963, 0.12728653848171234], "min": [0.7265206575393677, 1.9731227159500122, 0.02226206846535206], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1666428, "componentType": 5126, "count": 787, "max": [0.9102235436439514, 0.8980934619903564, 0.9995387196540833], "min": [-0.9922671318054199, -0.9941526055335999, -0.7285789847373962], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 563928, "componentType": 5126, "count": 787, "max": [0.08691886067390442, 0.8908979296684265], "min": [0.006821338552981615, 0.7807891368865967], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1325400, "componentType": 5125, "count": 4560, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1675872, "componentType": 5126, "count": 787, "max": [-0.7259973287582397, 2.2257673740386963, 0.12728653848171234], "min": [-0.895111620426178, 1.9731227159500122, 0.02226206846535206], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1685316, "componentType": 5126, "count": 787, "max": [0.9922686219215393, 0.8981146812438965, 0.999538004398346], "min": [-0.9102361798286438, -0.9941418766975403, -0.7285810708999634], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 570224, "componentType": 5126, "count": 787, "max": [0.08691886067390442, 0.8908979296684265], "min": [0.006821338552981615, 0.7807891368865967], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1343640, "componentType": 5125, "count": 4560, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1694760, "componentType": 5126, "count": 170, "max": [0.7515606880187988, 2.2957959175109863, 0.015067110769450665], "min": [0.5522304773330688, 2.201627731323242, -0.06148562952876091], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1696800, "componentType": 5126, "count": 170, "max": [0.7984815239906311, 0.9951212406158447, 0.9822224378585815], "min": [-0.8134767413139343, 0.17579267919063568, -0.9831063151359558], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 576520, "componentType": 5126, "count": 170, "max": [0.671704113483429, 0.9871429204940796], "min": [0.5531411170959473, 0.7508552074432373], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1361880, "componentType": 5125, "count": 879, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1698840, "componentType": 5126, "count": 167, "max": [-0.5522304773330688, 2.2957959175109863, 0.015067110769450665], "min": [-0.7513867020606995, 2.201627731323242, -0.06148562952876091], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1700844, "componentType": 5126, "count": 167, "max": [0.8193549513816833, 0.9946980476379395, 0.982164740562439], "min": [-0.8054831624031067, 0.17589697241783142, -0.9830123782157898], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 577880, "componentType": 5126, "count": 167, "max": [0.6717996001243591, 0.9871429204940796], "min": [0.5531268119812012, 0.7508552074432373], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 579216, "componentType": 5126, "count": 167, "max": [0.6717996001243591, 0.9871429204940796], "min": [0.5531268119812012, 0.7508552074432373], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1365396, "componentType": 5125, "count": 858, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1702848, "componentType": 5126, "count": 148, "max": [0.9313393235206604, 2.1699695587158203, 0.12525524199008942], "min": [-0.930817723274231, 1.9548280239105225, 0.04043255373835564], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1704624, "componentType": 5126, "count": 148, "max": [0.8466302156448364, 0.6462435126304626, 0.9926829934120178], "min": [-0.8465946912765503, 0.0487745963037014, -0.7883396148681641], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 580552, "componentType": 5126, "count": 148, "max": [0.16161489486694336, 0.6346059441566467], "min": [0.056733082979917526, 0.6085188984870911], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1368828, "componentType": 5125, "count": 696, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1706400, "componentType": 5126, "count": 413, "max": [0.37418562173843384, -2.0787665843963623, 0.503361165523529], "min": [-0.3736620843410492, -2.163515329360962, 0.490292489528656], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1711356, "componentType": 5126, "count": 413, "max": [0.492338627576828, 0.9999250769615173, 0.9735517501831055], "min": [-0.4923434853553772, -0.90880286693573, -0.9774706959724426], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 581736, "componentType": 5126, "count": 413, "max": [0.22223326563835144, 0.745086669921875], "min": [0.00891169160604477, 0.7073787450790405], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1371612, "componentType": 5125, "count": 2436, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1716312, "componentType": 5126, "count": 2644, "max": [0.9255126714706421, 2.422874689102173, 0.12714757025241852], "min": [-0.9249904751777649, 1.9474467039108276, -0.07142232358455658], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1748040, "componentType": 5126, "count": 2644, "max": [0.9821160435676575, 0.9944418668746948, 0.9998035430908203], "min": [-0.9821171164512634, -0.9832609295845032, -0.9999210834503174], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 585040, "componentType": 5126, "count": 2644, "max": [0.04775555804371834, 0.7949883937835693], "min": [0.029192142188549042, 0.7872351408004761], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1381356, "componentType": 5125, "count": 14508, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1779768, "componentType": 5126, "count": 427, "max": [0.37608668208122253, -2.088311195373535, 0.5067341327667236], "min": [-0.37556344270706177, -2.183875799179077, 0.4882372319698334], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1784892, "componentType": 5126, "count": 427, "max": [0.9894422292709351, 0.3859717845916748, 0.9961658716201782], "min": [-0.989520251750946, -0.9839988350868225, -0.9756771326065063], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 606192, "componentType": 5126, "count": 427, "max": [20.163049697875977, -13.911270141601562], "min": [14.166780471801758, -14.238969802856445], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1439388, "componentType": 5125, "count": 2304, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1790016, "componentType": 5126, "count": 193, "max": [0.14745599031448364, 0.3902979791164398, 0.6931470036506653], "min": [-0.14693376421928406, 0.3430725634098053, 0.6717985272407532], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1792332, "componentType": 5126, "count": 193, "max": [0.9274601340293884, 0.8950408101081848, -0.17577172815799713], "min": [-0.9274493455886841, -0.9745268821716309, -0.9824882745742798], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 609608, "componentType": 5126, "count": 193, "max": [0.22136268019676208, 0.6960960030555725], "min": [0.01654963195323944, 0.6522448062896729], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 611152, "componentType": 5126, "count": 193, "max": [0.22136268019676208, 0.6960960030555725], "min": [0.01654963195323944, 0.6522448062896729], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1448604, "componentType": 5125, "count": 960, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1794648, "componentType": 5126, "count": 181, "max": [-0.7527180910110474, 2.227919340133667, 0.05964551120996475], "min": [-0.9326347708702087, 2.0617682933807373, -0.05794685706496239], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1796820, "componentType": 5126, "count": 181, "max": [0.6883329153060913, 0.9946962594985962, 0.9865376949310303], "min": [-0.9413833618164062, 0.15864606201648712, -0.9258897304534912], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 612696, "componentType": 5126, "count": 181, "max": [0.8447909355163574, 0.9868714809417725], "min": [0.7338735461235046, 0.7420752048492432], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 614144, "componentType": 5126, "count": 181, "max": [0.8447909355163574, 0.9868714809417725], "min": [0.7338735461235046, 0.7420752048492432], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1452444, "componentType": 5125, "count": 924, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1798992, "componentType": 5126, "count": 181, "max": [0.9290006756782532, 2.227919340133667, 0.05964551120996475], "min": [0.7490839958190918, 2.0617682933807373, -0.05794685706496239], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1801164, "componentType": 5126, "count": 181, "max": [0.9377533793449402, 0.9930661916732788, 0.9865450859069824], "min": [-0.6763678193092346, 0.15866956114768982, -0.9293568134307861], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 615592, "componentType": 5126, "count": 181, "max": [0.8447909355163574, 0.9868714809417725], "min": [0.7338735461235046, 0.7420752048492432], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 617040, "componentType": 5126, "count": 181, "max": [0.8447909355163574, 0.9868714809417725], "min": [0.7338735461235046, 0.7420752048492432], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1456140, "componentType": 5125, "count": 924, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1803336, "componentType": 5126, "count": 14219, "max": [0.9756325483322144, 1.1257325410842896, 0.7501945495605469], "min": [-0.9751107096672058, -1.2542096376419067, -0.4147379398345947], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1973964, "componentType": 5126, "count": 14219, "max": [0.9999955892562866, 0.9989221096038818, 0.9999827742576599], "min": [-0.9999954104423523, -0.9999671578407288, -0.99997878074646], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 618488, "componentType": 5126, "count": 14219, "max": [871.9688110351562, 1351.6810302734375], "min": [606.9598999023438, -845.9005126953125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1459836, "componentType": 5125, "count": 83682, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2144592, "componentType": 5126, "count": 150, "max": [0.9177103042602539, 2.4192988872528076, -0.03354629501700401], "min": [-0.9171882271766663, 2.191164970397949, -0.09879551082849503], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2146392, "componentType": 5126, "count": 150, "max": [0.70127934217453, 0.9300385117530823, 0.48948222398757935], "min": [-0.7012751698493958, 0.6391911506652832, 0.27571573853492737], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 732240, "componentType": 5126, "count": 150, "max": [820.1865844726562, 2545.81103515625], "min": [474.4508056640625, 2335.174072265625], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1794564, "componentType": 5125, "count": 576, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2148192, "componentType": 5126, "count": 654, "max": [0.8217479586601257, 1.5465577840805054, 0.2287868708372116], "min": [-0.4280950427055359, 1.2923489809036255, 0.17762315273284912], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2156040, "componentType": 5126, "count": 654, "max": [0.9986847043037415, 0.6692129373550415, 0.462755411863327], "min": [-0.9976506233215332, -0.9966592192649841, -0.9902327060699463], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 733440, "componentType": 5126, "count": 654, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1796868, "componentType": 5125, "count": 3456, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2163888, "componentType": 5126, "count": 1930, "max": [-0.2865622937679291, 1.198571801185608, -0.025967711582779884], "min": [-0.5581645369529724, 1.0873756408691406, -0.24149738252162933], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2187048, "componentType": 5126, "count": 1930, "max": [1.0, 0.8646374344825745, 0.9998611807823181], "min": [-0.9930999279022217, -0.9999892711639404, -0.9983342885971069], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 738672, "componentType": 5126, "count": 1930, "max": [8.135588645935059, -16.57373046875], "min": [-33.41122817993164, -26.660690307617188], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1810692, "componentType": 5125, "count": 9984, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2210208, "componentType": 5126, "count": 6550, "max": [0.9318097233772278, 1.4410403966903687, 0.7131540775299072], "min": [-0.9312868118286133, -1.300235390663147, -0.425828218460083], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2288808, "componentType": 5126, "count": 6550, "max": [1.0, 0.9976444840431213, 0.9999986886978149], "min": [-1.0, -0.999995231628418, -0.9999982714653015], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 754112, "componentType": 5126, "count": 6550, "max": [12.999930381774902, 12.273360252380371], "min": [-11.999930381774902, -6.780025959014893], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1850628, "componentType": 5125, "count": 35544, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2367408, "componentType": 5126, "count": 1512, "max": [0.8341369032859802, 0.03348306566476822, 0.5473721027374268], "min": [-0.8336148262023926, -1.291237235069275, -0.39153432846069336], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2385552, "componentType": 5126, "count": 1512, "max": [0.999394953250885, 0.9993463754653931, 0.9978125691413879], "min": [-0.9993740320205688, -0.9994617700576782, -0.9963841438293457], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 806512, "componentType": 5126, "count": 1512, "max": [3.4513039588928223, 8.932022094726562], "min": [-1.2800819873809814, -9.592745780944824], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1992804, "componentType": 5125, "count": 8160, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2403696, "componentType": 5126, "count": 1314, "max": [0.76849365234375, 0.5074027180671692, -0.305919885635376], "min": [-0.7679712772369385, 0.3111485540866852, -0.3783498704433441], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2419464, "componentType": 5126, "count": 1314, "max": [0.9999850392341614, 0.9990603923797607, 0.9869543313980103], "min": [-0.9999849796295166, -0.9965268969535828, -0.9972509145736694], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 818608, "componentType": 5126, "count": 1314, "max": [15.852749824523926, 18.068429946899414], "min": [-16.920379638671875, -17.05695915222168], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2025444, "componentType": 5125, "count": 7392, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2435232, "componentType": 5126, "count": 1252, "max": [0.21186530590057373, 0.22431206703186035, -0.17694607377052307], "min": [-0.21134239435195923, -0.8756023645401001, -0.22259126603603363], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2450256, "componentType": 5126, "count": 1252, "max": [0.9992446303367615, 0.9983176589012146, 0.9988457560539246], "min": [-0.9992439150810242, -0.9981164932250977, -0.3628963828086853], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 829120, "componentType": 5126, "count": 1252, "max": [15.416890144348145, 8.202596664428711], "min": [0.5384129881858826, -14.367939949035645], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2055012, "componentType": 5125, "count": 6960, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2465280, "componentType": 5126, "count": 582, "max": [0.19310548901557922, 0.758726179599762, 0.3131677508354187], "min": [-0.19257710874080658, 0.6888359785079956, 0.016903279349207878], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2472264, "componentType": 5126, "count": 582, "max": [0.9998070597648621, 1.0, 0.9999467730522156], "min": [-0.9998073577880859, -0.986906111240387, -0.9981533288955688], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 839136, "componentType": 5126, "count": 582, "max": [18.751110076904297, 13.174909591674805], "min": [16.145280838012695, 10.469120025634766], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2082852, "componentType": 5125, "count": 3168, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2479248, "componentType": 5126, "count": 4748, "max": [1.0397453308105469, 1.406797170639038, 0.7501916885375977], "min": [-1.039223313331604, -1.7267322540283203, -0.011163235642015934], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2536224, "componentType": 5126, "count": 4748, "max": [0.9998021125793457, 0.9984828233718872, 0.9994257092475891], "min": [-0.9997991323471069, -0.9800335764884949, -0.9998960494995117], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 843792, "componentType": 5126, "count": 4748, "max": [0.24822001159191132, 0.8832329511642456], "min": [-0.16671599447727203, -2.2996349334716797], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2095524, "componentType": 5125, "count": 28224, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2593200, "componentType": 5126, "count": 1922, "max": [0.8517093658447266, -1.384697675704956, 0.12343265861272812], "min": [-0.8511882424354553, -1.5081850290298462, -0.34886661171913147], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2616264, "componentType": 5126, "count": 1922, "max": [1.0, 1.0, 0.998988151550293], "min": [-1.0, -1.0, -0.9988066554069519], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 881776, "componentType": 5126, "count": 1922, "max": [1.0, 1.0], "min": [0.0, 0.004414975643157959], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2208420, "componentType": 5125, "count": 9888, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2639328, "componentType": 5126, "count": 1922, "max": [0.8466460108757019, 1.817298412322998, 0.12247970700263977], "min": [-0.8461235761642456, 1.6938122510910034, -0.34981921315193176], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2662392, "componentType": 5126, "count": 1922, "max": [1.0, 1.0, 0.9989878535270691], "min": [-1.0, -1.0, -0.9988133311271667], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 897152, "componentType": 5126, "count": 1922, "max": [1.0, 1.0], "min": [0.0, 0.00284498929977417], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2247972, "componentType": 5125, "count": 9888, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2685456, "componentType": 5126, "count": 1968, "max": [0.7436172962188721, -0.1736953854560852, 0.38904905319213867], "min": [-0.7430943846702576, -0.23813100159168243, 0.32955193519592285], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2709072, "componentType": 5126, "count": 1968, "max": [0.9454180598258972, 0.9937995076179504, 0.9708532691001892], "min": [-0.945426344871521, -0.993567943572998, -0.9636805653572083], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 912528, "componentType": 5126, "count": 1968, "max": [0.6442720293998718, 0.3508070707321167], "min": [0.5922420024871826, 0.28478801250457764], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2287524, "componentType": 5125, "count": 11808, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2732688, "componentType": 5126, "count": 2026, "max": [1.0397368669509888, 1.3729904890060425, 0.04006672650575638], "min": [1.0273733139038086, 1.325528621673584, -0.005804920103400946], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2757000, "componentType": 5126, "count": 2026, "max": [0.9999720454216003, 0.9873588681221008, 0.9947131276130676], "min": [-0.2318175882101059, -0.988872230052948, -0.9737820029258728], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 928272, "componentType": 5126, "count": 2026, "max": [0.4497630000114441, 0.6155270338058472], "min": [0.36273902654647827, 0.13284802436828613], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2334756, "componentType": 5125, "count": 11244, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2781312, "componentType": 5126, "count": 2026, "max": [-1.0267629623413086, 1.3729584217071533, 0.0401264913380146], "min": [-1.0390347242355347, 1.3254894018173218, -0.0057419235818088055], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2805624, "componentType": 5126, "count": 2026, "max": [0.23137100040912628, 0.9874371290206909, 0.9943798780441284], "min": [-0.9999863505363464, -0.9892576932907104, -0.9737458229064941], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 944480, "componentType": 5126, "count": 2026, "max": [0.4497630000114441, 0.6155270338058472], "min": [0.36273902654647827, 0.13284802436828613], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2379732, "componentType": 5125, "count": 11244, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2829936, "componentType": 5126, "count": 24, "max": [-1.014894723892212, 1.310661792755127, -0.4874812960624695], "min": [-1.019049048423767, 1.1642032861709595, -0.4932713210582733], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2830224, "componentType": 5126, "count": 24, "max": [-0.9859424233436584, 0.16708514094352722, 0.0005353685119189322], "min": [-0.9999642372131348, -0.050149451941251755, -0.00016048632096499205], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 960688, "componentType": 5126, "count": 24, "max": [911.2490234375, 1520.366943359375], "min": [907.5347290039062, 1385.073974609375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2424708, "componentType": 5125, "count": 66, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2830512, "componentType": 5126, "count": 1263, "max": [0.8365472555160522, -2.2320868968963623, -0.44771426916122437], "min": [-0.8360255360603333, -2.5265603065490723, -0.5222460031509399], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2845668, "componentType": 5126, "count": 1263, "max": [0.9995352029800415, 0.018441766500473022, 0.9999951124191284], "min": [-0.9995287656784058, -0.9306011199951172, -0.9999951720237732], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 960880, "componentType": 5126, "count": 1263, "max": [747.6287841796875, -1752.7679443359375], "min": [-747.6287841796875, -2024.612060546875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2424972, "componentType": 5125, "count": 7152, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2860824, "componentType": 5126, "count": 1696, "max": [-0.24292854964733124, 0.7807910442352295, 0.17872823774814606], "min": [-0.6983308792114258, 0.7067573666572571, 0.14526435732841492], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2881176, "componentType": 5126, "count": 1696, "max": [0.9971131086349487, 0.974371612071991, 0.9990662336349487], "min": [-0.9970958232879639, -0.9704179763793945, -0.9990448355674744], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 970984, "componentType": 5126, "count": 1696, "max": [4.939125061035156, 4.496792793273926], "min": [3.1181609630584717, 2.047826051712036], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2453580, "componentType": 5125, "count": 9348, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2901528, "componentType": 5126, "count": 925, "max": [0.19303582608699799, 0.94329434633255, 0.31225529313087463], "min": [-0.19251294434070587, 0.6930938959121704, 0.010267576202750206], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2912628, "componentType": 5126, "count": 925, "max": [0.999941885471344, 0.9991981983184814, 0.9919869899749756], "min": [-0.9999423623085022, -0.9998472332954407, -0.994830846786499], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 984552, "componentType": 5126, "count": 925, "max": [10.550909996032715, 7.652155876159668], "min": [8.558206558227539, 5.69476318359375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2490972, "componentType": 5125, "count": 5256, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2923728, "componentType": 5126, "count": 870, "max": [0.9458054304122925, -0.08107214421033859, 0.750782310962677], "min": [-0.9451900124549866, -0.38191524147987366, 0.27493974566459656], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2934168, "componentType": 5126, "count": 870, "max": [0.9980534315109253, 0.9712107181549072, 0.9761890172958374], "min": [-0.9980459213256836, -0.9912630319595337, -0.980944037437439], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 991952, "componentType": 5126, "count": 870, "max": [841.8438720703125, 236.81089782714844], "min": [-841.8438720703125, -39.751399993896484], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2511996, "componentType": 5125, "count": 4800, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2944608, "componentType": 5126, "count": 4783, "max": [0.7965586185455322, 0.6484284400939941, 0.48732516169548035], "min": [-0.7960357069969177, -0.878088653087616, -0.3511783182621002], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3002004, "componentType": 5126, "count": 4783, "max": [1.0, 0.9999075531959534, 0.9999953508377075], "min": [-1.0, -0.9999896287918091, -0.9965276718139648], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 998912, "componentType": 5126, "count": 4783, "max": [17.607959747314453, 14.783160209655762], "min": [-4.742289066314697, -16.09408950805664], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2531196, "componentType": 5125, "count": 26256, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3059400, "componentType": 5126, "count": 3235, "max": [0.7906062006950378, 1.30972421169281, 0.24791812896728516], "min": [-0.7900833487510681, -0.15136432647705078, -0.32879236340522766], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3098220, "componentType": 5126, "count": 3235, "max": [1.0, 0.9999924898147583, 0.9999558329582214], "min": [-1.0, -1.0, -0.7881895899772644], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1037176, "componentType": 5126, "count": 3235, "max": [24.05443000793457, 17.990779876708984], "min": [-7.6729631423950195, -23.57383918762207], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2636220, "componentType": 5125, "count": 18180, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3137040, "componentType": 5126, "count": 3365, "max": [0.8484691977500916, 1.1343612670898438, 0.31294986605644226], "min": [-0.8479470014572144, -0.04696006700396538, -0.25715118646621704], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3177420, "componentType": 5126, "count": 3365, "max": [1.0, 0.9999971985816956, 0.9999955296516418], "min": [-1.0, -0.9998217225074768, -0.9999974370002747], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1063056, "componentType": 5126, "count": 3365, "max": [23.817710876464844, 12.272390365600586], "min": [-9.466768264770508, -27.198589324951172], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2708940, "componentType": 5125, "count": 18624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3217800, "componentType": 5126, "count": 124, "max": [0.8532071113586426, 1.6667780876159668, 0.28849491477012634], "min": [-0.8526848554611206, 1.1589714288711548, 0.12488334625959396], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3219288, "componentType": 5126, "count": 124, "max": [0.9992186427116394, 0.5488380193710327, 0.9883965253829956], "min": [-0.9992154240608215, -0.8363980054855347, -0.9181814193725586], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1089976, "componentType": 5126, "count": 124, "max": [0.0, 1851.4169921875], "min": [-762.5213012695312, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2783436, "componentType": 5125, "count": 480, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3220776, "componentType": 5126, "count": 1482, "max": [0.8014806509017944, -0.137199267745018, 0.6045641303062439], "min": [-0.8009582757949829, -0.32733508944511414, 0.39941978454589844], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3238560, "componentType": 5126, "count": 1482, "max": [0.9998179078102112, 0.9865943789482117, 0.9361565709114075], "min": [-0.9998193979263306, -0.9700275659561157, -0.9661127924919128], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1090968, "componentType": 5126, "count": 1482, "max": [5.671916961669922, -20.903919219970703], "min": [-5.648200988769531, -25.839099884033203], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2785356, "componentType": 5125, "count": 8064, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3256344, "componentType": 5126, "count": 4262, "max": [0.9315074682235718, 1.2981528043746948, 0.8209255337715149], "min": [-0.9309859871864319, -2.143615961074829, 0.1738027036190033], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3307488, "componentType": 5126, "count": 4262, "max": [0.9999961256980896, 0.9999951720237732, 0.9896736741065979], "min": [-0.9999961256980896, -0.999779224395752, -0.999946117401123], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1102824, "componentType": 5126, "count": 4262, "max": [0.5412099957466125, 29.230680465698242], "min": [-28.22015953063965, -28.075740814208984], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2817612, "componentType": 5125, "count": 23004, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3358632, "componentType": 5126, "count": 3354, "max": [0.665123701095581, -0.8307740092277527, -0.053914036601781845], "min": [-0.6645199656486511, -0.9493349194526672, -0.2833815813064575], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3398880, "componentType": 5126, "count": 3354, "max": [0.9998955726623535, 0.9986293315887451, 0.9999107122421265], "min": [-0.9999000430107117, -0.9989856481552124, -0.9980840682983398], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1136920, "componentType": 5126, "count": 3354, "max": [15.262630462646484, 14.025309562683105], "min": [4.258518218994141, -14.078100204467773], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2909628, "componentType": 5125, "count": 18960, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3439128, "componentType": 5126, "count": 2324, "max": [0.7698845267295837, 0.3988109529018402, 0.6858893632888794], "min": [-0.7693619132041931, -0.26430654525756836, 0.37180808186531067], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3467016, "componentType": 5126, "count": 2324, "max": [0.9991081953048706, 0.9985324740409851, 0.9995766878128052], "min": [-0.9991055130958557, -0.9988673329353333, -0.9958667159080505], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1163752, "componentType": 5126, "count": 2324, "max": [21.949020385742188, 0.8475090861320496], "min": [-32.31480026245117, -22.096839904785156], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2985468, "componentType": 5125, "count": 12720, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3494904, "componentType": 5126, "count": 3239, "max": [0.906644344329834, 1.2680811882019043, 0.0339093841612339], "min": [-0.906121551990509, -0.9488909244537354, -0.39227843284606934], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3533772, "componentType": 5126, "count": 3239, "max": [0.9831586480140686, 0.9999951720237732, 0.9999905228614807], "min": [-0.9831681847572327, -0.9876810312271118, 0.0031313071958720684], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1182344, "componentType": 5126, "count": 3239, "max": [2.1907598972320557, 1.806427001953125], "min": [-22.413530349731445, -14.202159881591797], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3036348, "componentType": 5125, "count": 18000, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3572640, "componentType": 5126, "count": 1373, "max": [-0.3736218512058258, 0.9712624549865723, 0.19866542518138885], "min": [-0.5675073862075806, 0.6995962858200073, -0.0019422713667154312], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3589116, "componentType": 5126, "count": 1373, "max": [0.9997194409370422, 0.9937478303909302, 0.999959409236908], "min": [-0.9999997019767761, -0.9854548573493958, -0.9999228119850159], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1208256, "componentType": 5126, "count": 1373, "max": [1.4634490013122559, 2.5234909057617188], "min": [0.49403902888298035, 1.126742959022522], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3108348, "componentType": 5125, "count": 7824, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3605592, "componentType": 5126, "count": 4270, "max": [0.7682483792304993, 0.5949533581733704, -0.18580885231494904], "min": [-0.7677260041236877, -0.06363087892532349, -0.4612579643726349], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3656832, "componentType": 5126, "count": 4270, "max": [0.999527633190155, 0.9988968968391418, 0.9987273812294006], "min": [-0.9995092153549194, -0.999271810054779, -0.9989295601844788], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1219240, "componentType": 5126, "count": 4270, "max": [6.5573320388793945, 8.40942668914795], "min": [-5.08657693862915, -5.877502918243408], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3139644, "componentType": 5125, "count": 24024, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3708072, "componentType": 5126, "count": 3256, "max": [0.8437880277633667, 1.5453776121139526, 0.8216180205345154], "min": [-0.8432657122612, -2.2171502113342285, 0.16603639721870422], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3747144, "componentType": 5126, "count": 3256, "max": [0.9661401510238647, 0.5526590347290039, 0.9999778866767883], "min": [-0.966098964214325, -0.9957640171051025, -0.9976686239242554], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1253400, "componentType": 5126, "count": 3256, "max": [754.1013793945312, 1739.1290283203125], "min": [-754.1013793945312, -1736.0469970703125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3235740, "componentType": 5125, "count": 16260, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3786216, "componentType": 5126, "count": 1259, "max": [1.0812968015670776, 2.6001572608947754, 0.020476073026657104], "min": [-1.080775260925293, -2.455547332763672, -0.5960351228713989], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3801324, "componentType": 5126, "count": 1259, "max": [1.0, 0.9998920559883118, 0.06111971288919449], "min": [-1.0, -0.9998371005058289, -0.9999999403953552], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1279448, "componentType": 5126, "count": 1259, "max": [0.02319200150668621, 0.0104600191116333], "min": [0.02319200150668621, 0.0104600191116333], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3300780, "componentType": 5125, "count": 6996, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3816432, "componentType": 5126, "count": 870, "max": [-0.31004947423934937, 0.7944844365119934, 0.16876184940338135], "min": [-0.6312095522880554, 0.7410573363304138, 0.1121239885687828], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3826872, "componentType": 5126, "count": 870, "max": [0.9833040833473206, 0.9871219992637634, 0.9948128461837769], "min": [-0.983363151550293, -0.9998226165771484, -0.9973205924034119], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1289520, "componentType": 5126, "count": 870, "max": [4.916559219360352, 5.117553234100342], "min": [4.0912580490112305, 4.1012492179870605], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3328764, "componentType": 5125, "count": 4704, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3837312, "componentType": 5126, "count": 4032, "max": [0.7959787249565125, 1.2291046380996704, -0.21240492165088654], "min": [-0.7954567074775696, -0.4029967784881592, -0.39208170771598816], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3885696, "componentType": 5126, "count": 4032, "max": [1.0, 0.9999831318855286, 1.0], "min": [-1.0, -0.9998560547828674, -0.8170353770256042], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1296480, "componentType": 5126, "count": 4032, "max": [2.2594759464263916, 1.8747860193252563], "min": [-1.2367370128631592, -0.659479022026062], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3347580, "componentType": 5125, "count": 21504, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3934080, "componentType": 5126, "count": 1968, "max": [0.6396241784095764, -0.8535352945327759, -0.07307703047990799], "min": [-0.6391016840934753, -0.9192519187927246, -0.10949885100126266], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3957696, "componentType": 5126, "count": 1968, "max": [0.997491180896759, 0.9999365210533142, 0.9963070750236511], "min": [-0.9974939227104187, -0.9999301433563232, -0.9985808730125427], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1328736, "componentType": 5126, "count": 1968, "max": [0.14971700310707092, 0.03407001495361328], "min": [0.09533900767564774, -0.02575504779815674], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3433596, "componentType": 5125, "count": 11808, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3981312, "componentType": 5126, "count": 778, "max": [0.9008867740631104, -1.2685844898223877, -0.29973500967025757], "min": [-0.9003666639328003, -1.6005955934524536, -0.523127019405365], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3990648, "componentType": 5126, "count": 778, "max": [0.9999662637710571, 0.9999940395355225, 1.0], "min": [-0.9999666213989258, -0.9999645948410034, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1344480, "componentType": 5126, "count": 778, "max": [0.6555630564689636, 0.707955002784729], "min": [0.47031304240226746, 0.5375840067863464], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3480828, "componentType": 5125, "count": 1764, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3999984, "componentType": 5126, "count": 778, "max": [0.8958236575126648, 1.9334129095077515, -0.3006875514984131], "min": [-0.8953019976615906, 1.6014015674591064, -0.5240797400474548], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4009320, "componentType": 5126, "count": 778, "max": [0.9999648332595825, 0.9999940395355225, 1.0], "min": [-0.9999662637710571, -0.9999644756317139, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1350704, "componentType": 5126, "count": 778, "max": [0.6555630564689636, 0.707955002784729], "min": [0.47031304240226746, 0.5375840067863464], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3487884, "componentType": 5125, "count": 1764, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4018656, "componentType": 5126, "count": 846, "max": [-0.2459179311990738, 0.7784093618392944, 0.17883554100990295], "min": [-0.6953416466712952, 0.7077934145927429, 0.15462307631969452], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4028808, "componentType": 5126, "count": 846, "max": [0.999422550201416, 0.9637916684150696, 0.999322235584259], "min": [-0.9994324445724487, -0.9635675549507141, -0.9039817452430725], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1356928, "componentType": 5126, "count": 846, "max": [0.9938340187072754, 0.9920859932899475], "min": [0.006610000040382147, 0.004905998706817627], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3494940, "componentType": 5125, "count": 4752, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4038960, "componentType": 5126, "count": 357, "max": [0.1898784637451172, 0.7567261457443237, 0.3102479577064514], "min": [-0.18935534358024597, 0.6893114447593689, 0.02032741717994213], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4043244, "componentType": 5126, "count": 357, "max": [0.9923083186149597, 0.11191539466381073, 0.9999983906745911], "min": [-0.9919251203536987, -0.9741504788398743, -0.9368288516998291], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1363696, "componentType": 5126, "count": 357, "max": [1.0, 1.0], "min": [0.0, 5.0067901611328125e-06], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1366552, "componentType": 5126, "count": 357, "max": [1.0, 1.0], "min": [0.0, 5.0067901611328125e-06], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3513948, "componentType": 5125, "count": 1704, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4047528, "componentType": 5126, "count": 23950, "max": [0.7916393280029297, 0.6436976790428162, 0.6468680500984192], "min": [-0.7911165952682495, -1.2786368131637573, -0.3389078378677368], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4334928, "componentType": 5126, "count": 23950, "max": [0.9999991059303284, 1.0, 1.0], "min": [-0.9999991059303284, -0.9999993443489075, -0.9999967217445374], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1369408, "componentType": 5126, "count": 23950, "max": [15.055879592895508, 18.064680099487305], "min": [-16.920730590820312, -14.005069732666016], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3520764, "componentType": 5125, "count": 133986, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4622328, "componentType": 5126, "count": 11242, "max": [0.8484649658203125, 0.9652919769287109, 0.7203922867774963], "min": [-0.8479426503181458, -1.98865807056427, -0.3121021091938019], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4757232, "componentType": 5126, "count": 11242, "max": [1.0, 0.9999975562095642, 0.9999991059303284], "min": [-1.0, -0.9999959468841553, -0.9999966621398926], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1561008, "componentType": 5126, "count": 11242, "max": [24.904069900512695, 10.356550216674805], "min": [-18.602205276489258, -25.82402229309082], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4056708, "componentType": 5125, "count": 64320, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4892136, "componentType": 5126, "count": 119, "max": [0.11788904666900635, 0.489521324634552, 0.6315181851387024], "min": [-0.11736588180065155, 0.483984112739563, 0.5544891953468323], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4893564, "componentType": 5126, "count": 119, "max": [0.9152223467826843, -0.3953036963939667, 0.9012956619262695], "min": [-0.9151508212089539, -0.999937117099762, -0.9079523682594299], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1650944, "componentType": 5126, "count": 119, "max": [0.8671876788139343, 0.8577378988265991], "min": [0.0950300395488739, 0.604912281036377], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4313988, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4894992, "componentType": 5126, "count": 1024, "max": [0.8382760286331177, -1.9033043384552002, 0.08766382187604904], "min": [-0.8377540707588196, -2.435913324356079, -0.21252575516700745], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4907280, "componentType": 5126, "count": 1024, "max": [0.9614643454551697, 0.7788438200950623, 0.944011390209198], "min": [-0.9614595174789429, -0.8660346269607544, -0.9292891025543213], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1651896, "componentType": 5126, "count": 1024, "max": [749.1740112304688, -773.0883178710938], "min": [617.9788818359375, -1264.489013671875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4316484, "componentType": 5125, "count": 6120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4919568, "componentType": 5126, "count": 2248, "max": [0.718436598777771, -0.37429866194725037, 0.25393351912498474], "min": [-0.7179136872291565, -2.946509599685669, -0.08547069132328033], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4946544, "componentType": 5126, "count": 2248, "max": [0.9762322306632996, 0.9991810321807861, 0.9999914765357971], "min": [-0.9762369990348816, -0.9999998211860657, -0.9999974370002747], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1660088, "componentType": 5126, "count": 2248, "max": [642.0380859375, 640.7728271484375], "min": [-0.0004880000196862966, -1736.3399658203125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4340964, "componentType": 5125, "count": 13464, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4973520, "componentType": 5126, "count": 3158, "max": [0.7101835012435913, -0.6164716482162476, -0.29673296213150024], "min": [-0.7096608281135559, -0.7930494546890259, -0.4774192273616791], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5011416, "componentType": 5126, "count": 3158, "max": [0.9997663497924805, 0.07641206681728363, 0.999988853931427], "min": [-0.9997668266296387, -0.9972379803657532, -0.9999890327453613], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1678072, "componentType": 5126, "count": 3158, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4394820, "componentType": 5125, "count": 17040, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5049312, "componentType": 5126, "count": 6947, "max": [0.8300300240516663, 0.01805877685546875, 0.0037140250205993652], "min": [-0.8295080065727234, -0.8896111249923706, -0.6663209795951843], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5132676, "componentType": 5126, "count": 6947, "max": [1.0, 0.9456478953361511, 0.9999954104423523], "min": [-1.0, -1.0, -0.9999956488609314], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1703336, "componentType": 5126, "count": 6947, "max": [0.6095470190048218, 0.5326549410820007], "min": [0.3255450129508972, 0.3173220157623291], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4462980, "componentType": 5125, "count": 33252, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5216040, "componentType": 5126, "count": 48, "max": [0.16304953396320343, -0.8232784867286682, -0.41823846101760864], "min": [-0.16304953396320343, -0.8472875356674194, -0.42531123757362366], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5216616, "componentType": 5126, "count": 48, "max": [1.0, 0.9994359612464905, 0.9994350671768188], "min": [-1.0, -0.9994353652000427, -0.9994350671768188], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1758912, "componentType": 5126, "count": 48, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4595988, "componentType": 5125, "count": 72, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5217192, "componentType": 5126, "count": 130, "max": [0.29064199328422546, -0.8234028220176697, -0.4236116409301758], "min": [-0.2901197075843811, -0.8679912090301514, -0.5886470675468445], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5218752, "componentType": 5126, "count": 130, "max": [1.0, 0.9994354844093323, 0.9994350671768188], "min": [-1.0, -0.9994354844093323, -0.9994350671768188], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1759296, "componentType": 5126, "count": 130, "max": [0.9917708039283752, 0.998375654220581], "min": [0.0016134381294250488, 0.010841608047485352], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4596276, "componentType": 5125, "count": 444, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5220312, "componentType": 5126, "count": 190, "max": [0.29575783014297485, -0.8218947052955627, -0.479579895734787], "min": [-0.2952355444431305, -0.8705186247825623, -0.5934323072433472], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5222592, "componentType": 5126, "count": 190, "max": [0.8946740031242371, -0.2680191993713379, 0.9569312930107117], "min": [-0.8946866393089294, -0.9631038904190063, -0.6518769264221191], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1760336, "componentType": 5126, "count": 190, "max": [0.9922789335250854, 0.8912129402160645], "min": [0.007721000351011753, 0.10525202751159668], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4598052, "componentType": 5125, "count": 912, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5224872, "componentType": 5126, "count": 54, "max": [0.20797401666641235, -0.8428843021392822, -0.5249601602554321], "min": [-0.23095977306365967, -0.8673408627510071, -0.5820639729499817], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5225520, "componentType": 5126, "count": 54, "max": [0.11180838197469711, -0.9481828212738037, 0.297402560710907], "min": [-0.11718321591615677, -0.9598449468612671, 0.2780212163925171], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1761856, "componentType": 5126, "count": 54, "max": [0.9917708039283752, 0.9425451755523682], "min": [0.0016134381294250488, 0.010841608047485352], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4601700, "componentType": 5125, "count": 222, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5226168, "componentType": 5126, "count": 2026, "max": [0.05360662192106247, -0.8332166075706482, -0.2780374586582184], "min": [-0.053093697875738144, -0.8542116284370422, -0.3785676658153534], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5250480, "componentType": 5126, "count": 2026, "max": [0.9886351227760315, 0.18733751773834229, 0.9968534111976624], "min": [-0.9883736968040466, -0.9999985098838806, -0.9829665422439575], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1762288, "componentType": 5126, "count": 2026, "max": [1.068552017211914, 0.8129809498786926], "min": [0.8737130165100098, 0.7343060374259949], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4602588, "componentType": 5125, "count": 11244, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5274792, "componentType": 5126, "count": 173, "max": [0.6881909370422363, -0.6881029605865479, -0.3761318027973175], "min": [0.524160623550415, -0.756895899772644, -0.38586047291755676], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5276868, "componentType": 5126, "count": 173, "max": [0.9838091731071472, 0.23446883261203766, 0.9991455674171448], "min": [-0.895954430103302, -0.9804372787475586, -0.9947254061698914], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1778496, "componentType": 5126, "count": 173, "max": [0.2264302521944046, 0.38814371824264526], "min": [0.012172549962997437, 0.34915220737457275], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4647564, "componentType": 5125, "count": 936, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5278944, "componentType": 5126, "count": 173, "max": [-0.5236382484436035, -0.6881029605865479, -0.3761318027973175], "min": [-0.6876685619354248, -0.7568943500518799, -0.38586047291755676], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5281020, "componentType": 5126, "count": 173, "max": [0.8957128524780273, 0.23374047875404358, 0.9991422295570374], "min": [-0.9838694930076599, -0.9808600544929504, -0.9947041273117065], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1779880, "componentType": 5126, "count": 173, "max": [0.2264302521944046, 0.3887138366699219], "min": [0.012172549962997437, 0.3497222661972046], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4651308, "componentType": 5125, "count": 936, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5283096, "componentType": 5126, "count": 2710, "max": [0.7005318999290466, -0.6525922417640686, -0.31085699796676636], "min": [-0.700164794921875, -0.7844427824020386, -0.41925492882728577], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5315616, "componentType": 5126, "count": 2710, "max": [0.9998210668563843, 0.23914699256420135, 0.9999954104423523], "min": [-0.9998987317085266, -0.9911083579063416, -0.9999955296516418], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1781264, "componentType": 5126, "count": 2710, "max": [0.562472939491272, 0.35127103328704834], "min": [-0.16671599447727203, -5.666133880615234], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4655052, "componentType": 5125, "count": 15552, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5348136, "componentType": 5126, "count": 327, "max": [0.7010135054588318, -0.6596202254295349, -0.3108663856983185], "min": [0.4530561864376068, -0.7869500517845154, -0.4193670451641083], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5352060, "componentType": 5126, "count": 327, "max": [0.9945189356803894, 0.013052466325461864, 0.8065882921218872], "min": [-0.5767461061477661, -0.9536505341529846, -0.9999915957450867], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1802944, "componentType": 5126, "count": 327, "max": [0.4098745882511139, 0.9872615933418274], "min": [0.014246433973312378, 0.8864851593971252], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1805560, "componentType": 5126, "count": 327, "max": [0.4098745882511139, 0.9872615933418274], "min": [0.014246433973312378, 0.8864851593971252], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4717260, "componentType": 5125, "count": 1776, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5355984, "componentType": 5126, "count": 327, "max": [-0.452534019947052, -0.6596202254295349, -0.3108663856983185], "min": [-0.7004918456077576, -0.7869500517845154, -0.4193670451641083], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5359908, "componentType": 5126, "count": 327, "max": [0.576744794845581, 0.01305173709988594, 0.8065884709358215], "min": [-0.9945153594017029, -0.9536547660827637, -0.9999915957450867], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1808176, "componentType": 5126, "count": 327, "max": [0.4098745882511139, 0.9872615933418274], "min": [0.014246433973312378, 0.8864851593971252], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4724364, "componentType": 5125, "count": 1776, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5363832, "componentType": 5126, "count": 346, "max": [0.6881909370422363, -0.6885194778442383, -0.3761318027973175], "min": [-0.6876685619354248, -0.7573124170303345, -0.38586047291755676], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5367984, "componentType": 5126, "count": 346, "max": [0.9838142991065979, 0.23447321355342865, 0.9991458058357239], "min": [-0.9838694930076599, -0.9808509945869446, -0.9947254061698914], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1810792, "componentType": 5126, "count": 346, "max": [0.16382890939712524, 0.3318047523498535], "min": [0.04016457125544548, 0.3092998266220093], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4731468, "componentType": 5125, "count": 1872, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5372136, "componentType": 5126, "count": 1438, "max": [0.710465669631958, -0.6514542102813721, -0.29644808173179626], "min": [-0.7099432945251465, -0.7974466681480408, -0.47408783435821533], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5389392, "componentType": 5126, "count": 1438, "max": [0.996268630027771, 0.4408443570137024, 0.9945419430732727], "min": [-0.9962676167488098, -0.9972935914993286, -0.9687511920928955], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1813560, "componentType": 5126, "count": 1438, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4738956, "componentType": 5125, "count": 8184, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5406648, "componentType": 5126, "count": 79, "max": [0.1721053123474121, -0.09597793221473694, 0.32878148555755615], "min": [0.03942139074206352, -1.208676815032959, -0.3094085156917572], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5407596, "componentType": 5126, "count": 79, "max": [1.0, 0.9932376742362976, 0.7054582834243774], "min": [-0.6033182144165039, -0.9932378530502319, -0.7458991408348083], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1825064, "componentType": 5126, "count": 79, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4771692, "componentType": 5125, "count": 270, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5408544, "componentType": 5126, "count": 541, "max": [0.1696022003889084, -0.6708939075469971, -0.016435308381915092], "min": [0.1343514770269394, -0.7499446272850037, -0.020428039133548737], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5415036, "componentType": 5126, "count": 541, "max": [1.0, 0.9999922513961792, 0.9999760389328003], "min": [-1.0, -0.9999935030937195, -0.014497178606688976], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1825696, "componentType": 5126, "count": 541, "max": [0.3840640187263489, 0.1961050033569336], "min": [0.001118999905884266, 0.000782012939453125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4772772, "componentType": 5125, "count": 2880, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5421528, "componentType": 5126, "count": 1399, "max": [0.11763933300971985, -0.029988188296556473, 0.36475667357444763], "min": [-0.21809618175029755, -1.3217324018478394, 0.19919990003108978], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5438316, "componentType": 5126, "count": 1399, "max": [0.9999090433120728, 0.9985044002532959, 0.999489426612854], "min": [-0.9970898032188416, -0.9982680678367615, -0.9988307356834412], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1830024, "componentType": 5126, "count": 1399, "max": [1.8542660474777222, 1.5643160343170166], "min": [-0.177916020154953, -1.725451946258545], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4784292, "componentType": 5125, "count": 8094, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5455104, "componentType": 5126, "count": 1163, "max": [0.13524344563484192, -0.10721148550510406, 0.22066493332386017], "min": [0.058523841202259064, -1.1884527206420898, -0.28345298767089844], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5469060, "componentType": 5126, "count": 1163, "max": [0.9999978542327881, 0.9999831318855286, 0.9999988675117493], "min": [-0.2490530014038086, -0.9999277591705322, -0.9999999403953552], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1841216, "componentType": 5126, "count": 1163, "max": [6.999117851257324, 5.0815958976745605], "min": [-5.952136993408203, -2.0075509548187256], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4816668, "componentType": 5125, "count": 6504, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5483016, "componentType": 5126, "count": 3481, "max": [0.17712809145450592, -0.3146202266216278, 0.2076292484998703], "min": [0.08666335791349411, -1.1853001117706299, -0.09501732885837555], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5524788, "componentType": 5126, "count": 3481, "max": [1.0, 0.9999954700469971, 0.9999995827674866], "min": [-1.0, -0.9999954104423523, -0.9999977350234985], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1850520, "componentType": 5126, "count": 3481, "max": [6.312979221343994, 4.485674858093262], "min": [-5.315323829650879, -3.1329116821289062], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4842684, "componentType": 5125, "count": 19548, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5566560, "componentType": 5126, "count": 2642, "max": [0.17258435487747192, -0.10721148550510406, 0.466895192861557], "min": [0.04176803305745125, -1.2110916376113892, -0.2991441786289215], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5598264, "componentType": 5126, "count": 2642, "max": [1.0, 0.9999905228614807, 0.9999961853027344], "min": [-0.9996763467788696, -0.9998148083686829, -0.9999775886535645], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1878368, "componentType": 5126, "count": 2642, "max": [3.455029010772705, 3.488607883453369], "min": [-3.0107109546661377, -3.124584197998047], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4920876, "componentType": 5125, "count": 14778, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5629968, "componentType": 5126, "count": 119, "max": [0.16556431353092194, -0.4020172357559204, 0.1619226336479187], "min": [0.15758678317070007, -0.505111813545227, 0.10575132071971893], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5631396, "componentType": 5126, "count": 119, "max": [0.9990038871765137, 0.9575751423835754, 0.9967217445373535], "min": [-0.9446342587471008, -0.9631311893463135, -0.9623038172721863], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1899504, "componentType": 5126, "count": 119, "max": [0.6634640693664551, 1.3218899965286255], "min": [-0.08742599934339523, 0.6890360116958618], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4979988, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5632824, "componentType": 5126, "count": 1393, "max": [0.17208445072174072, -0.6651589274406433, -0.0171542689204216], "min": [0.13177284598350525, -0.7524266839027405, -0.024085884913802147], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5649540, "componentType": 5126, "count": 1393, "max": [1.0, 0.9999422430992126, 0.9999969601631165], "min": [-1.0, -0.9999028444290161, -0.014892345294356346], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1900456, "componentType": 5126, "count": 1393, "max": [1.5069879293441772, 1.6614699363708496], "min": [-0.9437980651855469, -2.146312952041626], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4982484, "componentType": 5125, "count": 7152, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5666256, "componentType": 5126, "count": 261, "max": [0.16776184737682343, -0.10707224905490875, -0.09222067892551422], "min": [0.12958602607250214, -0.518240213394165, -0.2993556559085846], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5669388, "componentType": 5126, "count": 261, "max": [0.9988020658493042, 0.994006335735321, 0.985792875289917], "min": [-0.2623978555202484, -0.967450737953186, -0.9964103102684021], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1911600, "componentType": 5126, "count": 261, "max": [2.6307060718536377, 9.521265029907227], "min": [-7.047637939453125, -9.143024444580078], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5011092, "componentType": 5125, "count": 1440, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5672520, "componentType": 5126, "count": 441, "max": [0.1539369374513626, -0.07776240259408951, 0.4969482421875], "min": [0.09091565012931824, -0.38400697708129883, 0.2967909872531891], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5677812, "componentType": 5126, "count": 441, "max": [0.9982230067253113, 0.9538951516151428, 0.9179186224937439], "min": [-0.9934277534484863, -0.9867733716964722, -0.9855358004570007], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1913688, "componentType": 5126, "count": 441, "max": [818.5885009765625, 1326.5479736328125], "min": [761.4711303710938, 1042.51904296875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5016852, "componentType": 5125, "count": 2376, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5683104, "componentType": 5126, "count": 458, "max": [0.2660951614379883, -0.05781150609254837, 0.7821136116981506], "min": [0.06068749353289604, -1.4266422986984253, 0.271451473236084], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5688600, "componentType": 5126, "count": 458, "max": [0.968954861164093, 0.8213109374046326, 0.9702603220939636], "min": [-0.9549990892410278, -0.6969594955444336, -0.8976561427116394], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1917216, "componentType": 5126, "count": 458, "max": [842.9661865234375, 1343.8819580078125], "min": [661.7003784179688, 80.7935562133789], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5026356, "componentType": 5125, "count": 2736, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5694096, "componentType": 5126, "count": 224, "max": [-0.0037190003786236048, -0.37453141808509827, 0.44014617800712585], "min": [-0.21331313252449036, -0.47847360372543335, 0.2953089773654938], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5696784, "componentType": 5126, "count": 224, "max": [0.4445626437664032, -0.8957371115684509, 0.004452094901353121], "min": [0.44440749287605286, -0.8958144187927246, 0.004225169774144888], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1920880, "componentType": 5126, "count": 224, "max": [0.6742305159568787, 0.9664826393127441], "min": [0.15491949021816254, 0.49973762035369873], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5037300, "componentType": 5125, "count": 1200, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5699472, "componentType": 5126, "count": 4892, "max": [0.09265327453613281, 0.003240346908569336, 0.44372421503067017], "min": [-0.21709465980529785, -1.3157845735549927, -0.42496877908706665], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5758176, "componentType": 5126, "count": 4892, "max": [0.9926525950431824, 1.0, 0.9981119632720947], "min": [-0.9999988675117493, -0.9998851418495178, -0.9999669194221497], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1922672, "componentType": 5126, "count": 4892, "max": [0.9247520565986633, 0.8063569664955139], "min": [0.5519059896469116, 0.4785110354423523], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5042100, "componentType": 5125, "count": 25656, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5816880, "componentType": 5126, "count": 2841, "max": [0.09899842739105225, 0.003240346908569336, 0.34151023626327515], "min": [-0.06111466884613037, -1.3165385723114014, -0.42502278089523315], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5850972, "componentType": 5126, "count": 2841, "max": [0.9999988675117493, 1.0, 0.9995335340499878], "min": [-0.15439191460609436, -0.9999875426292419, -0.9999435544013977], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1961808, "componentType": 5126, "count": 2841, "max": [0.9997729659080505, 0.8467079997062683], "min": [0.00022700001136399806, 0.23740100860595703], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5144724, "componentType": 5125, "count": 13338, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5885064, "componentType": 5126, "count": 3, "max": [0.06490129232406616, -1.217079520225525, 0.3096364140510559], "min": [0.0605352520942688, -1.2390475273132324, 0.3015454113483429], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5885100, "componentType": 5126, "count": 3, "max": [0.8930534720420837, -0.015911664813756943, -0.44966915249824524], "min": [0.8720380663871765, -0.019320432096719742, -0.489059180021286], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1984536, "componentType": 5126, "count": 3, "max": [0.8045950531959534, 0.8025130033493042], "min": [0.8031060695648193, 0.798876941204071], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5198076, "componentType": 5125, "count": 3, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5885136, "componentType": 5126, "count": 14, "max": [0.26112231612205505, -0.12917663156986237, 0.3054358959197998], "min": [0.1537671685218811, -0.9783158302307129, 0.22286026179790497], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5885304, "componentType": 5126, "count": 14, "max": [0.9959476590156555, 0.9999951124191284, 0.17440053820610046], "min": [-0.10660425573587418, -0.9269169569015503, -0.4652078151702881], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1984560, "componentType": 5126, "count": 14, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5198088, "componentType": 5125, "count": 30, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5885472, "componentType": 5126, "count": 115, "max": [0.22996047139167786, -0.5879384279251099, 0.04909978806972504], "min": [0.21103493869304657, -0.6021753549575806, 0.04642916098237038], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5886852, "componentType": 5126, "count": 115, "max": [0.9976096749305725, 0.999953031539917, 0.9995522499084473], "min": [-0.9982702136039734, -0.9997904896736145, -0.06117992848157883], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1984672, "componentType": 5126, "count": 115, "max": [0.19426999986171722, 0.1958799958229065], "min": [0.0008880000677891076, 0.0013620257377624512], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5198208, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5888232, "componentType": 5126, "count": 34, "max": [0.1996111273765564, -0.9952206611633301, 0.4301545023918152], "min": [0.16611763834953308, -1.174920678138733, 0.40924882888793945], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5888640, "componentType": 5126, "count": 34, "max": [0.9125500917434692, 0.1640903204679489, 0.9977426528930664], "min": [0.030653173103928566, -0.9404767155647278, 0.3042135536670685], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1985592, "componentType": 5126, "count": 34, "max": [0.6566460132598877, 0.6071880459785461], "min": [0.650894045829773, 0.5820339322090149], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5200704, "componentType": 5125, "count": 96, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5889048, "componentType": 5126, "count": 789, "max": [0.22523656487464905, -0.01615535467863083, 0.45722535252571106], "min": [0.014780036173760891, -1.199746012687683, 0.24184966087341309], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5898516, "componentType": 5126, "count": 789, "max": [0.9955940246582031, 0.984128475189209, 0.9623706936836243], "min": [-0.988251805305481, -0.9907670617103577, -0.9977532625198364], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1985864, "componentType": 5126, "count": 789, "max": [1.44868803024292, 2.7254550457000732], "min": [-0.6706569790840149, -0.8819609880447388], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5201088, "componentType": 5125, "count": 4626, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5907984, "componentType": 5126, "count": 1069, "max": [0.2621002197265625, -0.09165183454751968, 0.29831111431121826], "min": [0.13667233288288116, -0.9775084257125854, -0.22343960404396057], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5920812, "componentType": 5126, "count": 1069, "max": [0.999995231628418, 0.9999986290931702, 0.9999940395355225], "min": [-0.2401605248451233, -0.999698281288147, -0.9999966621398926], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1992176, "componentType": 5126, "count": 1069, "max": [5.371909141540527, 3.2401340007781982], "min": [-3.6754770278930664, -5.104167938232422], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5219592, "componentType": 5125, "count": 5946, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5933640, "componentType": 5126, "count": 3346, "max": [0.2571183443069458, -0.17009152472019196, 0.2850644886493683], "min": [0.10891728103160858, -0.9722708463668823, -0.0305352620780468], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5973792, "componentType": 5126, "count": 3346, "max": [0.9999921321868896, 0.9999998807907104, 0.999968945980072], "min": [-0.9998741149902344, -0.9998260736465454, -0.999755322933197], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2000728, "componentType": 5126, "count": 3346, "max": [3.413830041885376, 6.35478401184082], "min": [-4.46377420425415, -5.059726238250732], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5243376, "componentType": 5125, "count": 18708, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6013944, "componentType": 5126, "count": 1863, "max": [0.26515913009643555, -0.08100012689828873, 0.4124729335308075], "min": [0.06557044386863708, -1.0096288919448853, -0.26374876499176025], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6036300, "componentType": 5126, "count": 1863, "max": [0.9993675947189331, 0.9997565150260925, 0.9998605847358704], "min": [-0.9923430681228638, -0.9997414350509644, -0.9999935030937195], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2027496, "componentType": 5126, "count": 1863, "max": [3.401458978652954, 2.7488579750061035], "min": [-1.4671390056610107, -0.16361093521118164], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5318208, "componentType": 5125, "count": 10470, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6058656, "componentType": 5126, "count": 119, "max": [0.2296464741230011, -0.2579402029514313, 0.22179214656352997], "min": [0.21556740999221802, -0.36319342255592346, 0.16570009291172028], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6060084, "componentType": 5126, "count": 119, "max": [0.9992653727531433, 0.9690073132514954, 0.9982384443283081], "min": [-0.9578866362571716, -0.9505559802055359, -0.9695324897766113], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2042400, "componentType": 5126, "count": 119, "max": [0.31431400775909424, -0.420132040977478], "min": [-0.37035101652145386, -1.2499299049377441], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5360088, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6061512, "componentType": 5126, "count": 275, "max": [0.23256707191467285, -0.57072514295578, 0.04912133142352104], "min": [0.20823070406913757, -0.6043902039527893, 0.04070263355970383], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6064812, "componentType": 5126, "count": 275, "max": [0.9997152090072632, 0.9995849132537842, 0.9998363852500916], "min": [-0.9983376264572144, -0.9999392032623291, -0.07116450369358063], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2043352, "componentType": 5126, "count": 275, "max": [737.822509765625, -337.03021240234375], "min": [716.0662841796875, -368.13299560546875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5362584, "componentType": 5125, "count": 1584, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6068112, "componentType": 5126, "count": 864, "max": [0.32693740725517273, -0.12917347252368927, 0.7870416045188904], "min": [0.09597644209861755, -1.106730580329895, 0.3485170304775238], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6078480, "componentType": 5126, "count": 864, "max": [0.9773876667022705, 0.7203779220581055, 0.9793654680252075], "min": [-0.9537559747695923, -0.7751089334487915, -0.9117996692657471], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2045552, "componentType": 5126, "count": 864, "max": [835.1527099609375, 71.75076293945312], "min": [631.640380859375, -830.1400146484375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5368920, "componentType": 5125, "count": 5172, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6088848, "componentType": 5126, "count": 3660, "max": [0.20222312211990356, 0.043471336364746094, 0.43355780839920044], "min": [-0.046562790870666504, -1.2384306192398071, -0.409845232963562], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6132768, "componentType": 5126, "count": 3660, "max": [0.3884371817111969, 0.9998904466629028, 0.9913590550422668], "min": [-0.9999963641166687, -0.9990784525871277, -0.9980074763298035], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2052464, "componentType": 5126, "count": 3660, "max": [0.9168429970741272, 0.31668800115585327], "min": [0.7630410194396973, 0.07025200128555298], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5389608, "componentType": 5125, "count": 18402, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6176688, "componentType": 5126, "count": 1934, "max": [0.20280611515045166, 0.04337833821773529, 0.4311148226261139], "min": [-0.039302825927734375, -1.238310694694519, -0.41281723976135254], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6199896, "componentType": 5126, "count": 1934, "max": [0.9999977350234985, 0.9999961853027344, 0.9978140592575073], "min": [-0.12122451514005661, -0.9860734343528748, -0.9999996423721313], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2081744, "componentType": 5126, "count": 1934, "max": [0.6286120414733887, 0.6772180199623108], "min": [0.2374260127544403, 0.0002269744873046875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5463216, "componentType": 5125, "count": 9168, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6223104, "componentType": 5126, "count": 3, "max": [0.07716649770736694, -0.1583191156387329, 0.30104219913482666], "min": [0.07408750057220459, -0.18900611996650696, 0.2946942150592804], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6223140, "componentType": 5126, "count": 3, "max": [-0.8948360085487366, 0.0037065809592604637, 0.4463799297809601], "min": [-0.9099186658859253, -0.000970425084233284, 0.4147856533527374], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2097216, "componentType": 5126, "count": 3, "max": [0.7708020210266113, 0.16515398025512695], "min": [0.7670290470123291, 0.1614370346069336], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5499888, "componentType": 5125, "count": 3, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6223176, "componentType": 5126, "count": 79, "max": [-0.038711972534656525, -0.09597855806350708, 0.32878148555755615], "min": [-0.17139583826065063, -1.2086772918701172, -0.3094085156917572], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6224124, "componentType": 5126, "count": 79, "max": [0.6033182144165039, 0.9932379722595215, 0.7054193615913391], "min": [-1.0, -0.9932379126548767, -0.7458992004394531], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2097240, "componentType": 5126, "count": 79, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5499900, "componentType": 5125, "count": 270, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6225072, "componentType": 5126, "count": 115, "max": [-0.13914421200752258, -0.7269451022148132, -0.017879744991660118], "min": [-0.15766504406929016, -0.7410322427749634, -0.02028823085129261], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6226452, "componentType": 5126, "count": 115, "max": [0.999051570892334, 0.9999441504478455, 0.9999552369117737], "min": [-0.999991238117218, -0.9997054934501648, -0.003786447923630476], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2097872, "componentType": 5126, "count": 115, "max": [0.19344200193881989, 0.1958550214767456], "min": [0.0009340000106021762, 0.0018079876899719238], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5500980, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6227832, "componentType": 5126, "count": 1431, "max": [0.2188059687614441, -0.029987813904881477, 0.3647570312023163], "min": [-0.1169305294752121, -1.3217321634292603, 0.19919990003108978], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6245004, "componentType": 5126, "count": 1431, "max": [0.9970906972885132, 0.9985097646713257, 0.9994968175888062], "min": [-0.9998932480812073, -0.9982621073722839, -0.9988334774971008], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2098792, "componentType": 5126, "count": 1431, "max": [1.449512004852295, 2.7254550457000732], "min": [-0.177916020154953, -1.7254538536071777], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5503476, "componentType": 5125, "count": 8286, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6262176, "componentType": 5126, "count": 1163, "max": [-0.05781405046582222, -0.10721197724342346, 0.22066493332386017], "min": [-0.13453322649002075, -1.1884528398513794, -0.28345298767089844], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6276132, "componentType": 5126, "count": 1163, "max": [0.24902357161045074, 0.9999831914901733, 0.9999988079071045], "min": [-0.999997615814209, -0.9999277591705322, -0.9999999403953552], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2110240, "componentType": 5126, "count": 1163, "max": [6.999117851257324, 5.0815958976745605], "min": [-5.952136993408203, -2.0075509548187256], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5536620, "componentType": 5125, "count": 6504, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6290088, "componentType": 5126, "count": 3481, "max": [-0.08595345169305801, -0.31462034583091736, 0.2076292484998703], "min": [-0.17641812562942505, -1.185300350189209, -0.09501732885837555], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6331860, "componentType": 5126, "count": 3481, "max": [1.0, 0.9999954104423523, 0.9999996423721313], "min": [-1.0, -0.999997079372406, -0.9999977350234985], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2119544, "componentType": 5126, "count": 3481, "max": [6.312979221343994, 4.485674858093262], "min": [-5.315323829650879, -3.1329116821289062], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5562636, "componentType": 5125, "count": 19548, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6373632, "componentType": 5126, "count": 2642, "max": [-0.0410582460463047, -0.10721197724342346, 0.466895192861557], "min": [-0.1718740165233612, -1.2110918760299683, -0.2991441786289215], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6405336, "componentType": 5126, "count": 2642, "max": [0.9996418356895447, 0.9999904632568359, 0.9999961853027344], "min": [-1.0, -0.9998142719268799, -0.9999775886535645], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2147392, "componentType": 5126, "count": 2642, "max": [3.455029010772705, 3.488607883453369], "min": [-3.0107109546661377, -3.124584197998047], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5640828, "componentType": 5125, "count": 14778, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6437040, "componentType": 5126, "count": 119, "max": [-0.15687650442123413, -0.40201735496520996, 0.1619226336479187], "min": [-0.164854034781456, -0.5051121711730957, 0.10575132071971893], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6438468, "componentType": 5126, "count": 119, "max": [0.9446420669555664, 0.9576246738433838, 0.9966357946395874], "min": [-0.9990023374557495, -0.9631302952766418, -0.9622412919998169], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2168528, "componentType": 5126, "count": 119, "max": [0.6634640693664551, 1.3218899965286255], "min": [-0.08742599934339523, 0.6890360116958618], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5699940, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6439896, "componentType": 5126, "count": 275, "max": [-0.1366477608680725, -0.7097861766815186, -0.017891140654683113], "min": [-0.16019847989082336, -0.7433140277862549, -0.02573881857097149], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6443196, "componentType": 5126, "count": 275, "max": [0.9999732375144958, 0.9998758435249329, 0.9999954700469971], "min": [-0.9999955892562866, -0.9997358322143555, -0.017413262277841568], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2169480, "componentType": 5126, "count": 275, "max": [0.9995230436325073, 0.9999319911003113], "min": [0.005507000256329775, 0.0005729794502258301], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5702436, "componentType": 5125, "count": 1584, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6446496, "componentType": 5126, "count": 261, "max": [-0.1288760006427765, -0.1070723682641983, -0.09222067892551422], "min": [-0.16705138981342316, -0.518240213394165, -0.2993556559085846], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6449628, "componentType": 5126, "count": 261, "max": [0.2625807523727417, 0.9939942359924316, 0.9857916831970215], "min": [-0.9988006353378296, -0.9674599170684814, -0.9964061379432678], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2171680, "componentType": 5126, "count": 261, "max": [2.6307060718536377, 9.521265029907227], "min": [-7.047637939453125, -9.143024444580078], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5708772, "componentType": 5125, "count": 1440, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6452760, "componentType": 5126, "count": 458, "max": [-0.05997709929943085, -0.057812366634607315, 0.7821136116981506], "min": [-0.26538458466529846, -1.4266425371170044, 0.271451473236084], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6458256, "componentType": 5126, "count": 458, "max": [0.9550098776817322, 0.8212966322898865, 0.9702798128128052], "min": [-0.9689515233039856, -0.6969638466835022, -0.8976749181747437], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2173768, "componentType": 5126, "count": 458, "max": [842.9661865234375, 1343.8819580078125], "min": [661.7003784179688, 80.7935562133789], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5714532, "componentType": 5125, "count": 2736, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6463752, "componentType": 5126, "count": 224, "max": [0.21402327716350555, -0.3745318055152893, 0.44014617800712585], "min": [0.004429583437740803, -0.4784737229347229, 0.2953089773654938], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6466440, "componentType": 5126, "count": 224, "max": [-0.4562108516693115, -0.8897907733917236, 0.004413023591041565], "min": [-0.4563480317592621, -0.8898612856864929, 0.0041685025207698345], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2177432, "componentType": 5126, "count": 224, "max": [0.6725488305091858, 0.9557483792304993], "min": [0.15323780477046967, 0.48900336027145386], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5725476, "componentType": 5125, "count": 1200, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6469128, "componentType": 5126, "count": 4882, "max": [0.21780240535736084, 0.003240346908569336, 0.44372421503067017], "min": [-0.09194254875183105, -1.3157845735549927, -0.42496877908706665], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6527712, "componentType": 5126, "count": 4882, "max": [0.9999988675117493, 1.0, 0.99811851978302], "min": [-0.992463231086731, -0.9998852014541626, -0.9999669194221497], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2179224, "componentType": 5126, "count": 4882, "max": [0.8917250037193298, 0.5859809517860413], "min": [0.3255450129508972, 0.3173220157623291], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5730276, "componentType": 5125, "count": 25656, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6586296, "componentType": 5126, "count": 2735, "max": [0.06182241439819336, 0.003240346908569336, 0.34151023626327515], "min": [-0.09828853607177734, -1.3165396451950073, -0.42502278089523315], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6619116, "componentType": 5126, "count": 2735, "max": [0.15438750386238098, 0.9999999403953552, 0.9993090629577637], "min": [-0.9999998211860657, -0.9999865293502808, -0.9999435544013977], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2218280, "componentType": 5126, "count": 2735, "max": [0.9997640252113342, 0.9051820635795593], "min": [0.2374260127544403, 0.23740100860595703], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5832900, "componentType": 5125, "count": 13338, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6651936, "componentType": 5126, "count": 3, "max": [-0.04692554473876953, -1.1773476600646973, 0.30694541335105896], "min": [-0.05129152536392212, -1.1993156671524048, 0.29885438084602356], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6651972, "componentType": 5126, "count": 3, "max": [0.893057644367218, 0.019318493083119392, 0.4851321578025818], "min": [0.8742368221282959, 0.01591254398226738, 0.4496609568595886], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2240160, "componentType": 5126, "count": 3, "max": [0.8853709697723389, 0.5298150181770325], "min": [0.883849024772644, 0.5261779427528381], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5886252, "componentType": 5125, "count": 3, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6652008, "componentType": 5126, "count": 14, "max": [-0.15305174887180328, -0.1291767805814743, 0.3054358959197998], "min": [-0.2604067623615265, -0.9783156514167786, 0.22286026179790497], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6652176, "componentType": 5126, "count": 14, "max": [0.10673218965530396, 0.999995231628418, 0.17439144849777222], "min": [-0.9959518909454346, -0.9269219040870667, -0.4651551842689514], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2240184, "componentType": 5126, "count": 14, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5886264, "componentType": 5125, "count": 30, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6652344, "componentType": 5126, "count": 115, "max": [-0.21031945943832397, -0.5879385471343994, 0.04909978806972504], "min": [-0.22924426198005676, -0.6021760106086731, 0.04642916098237038], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6653724, "componentType": 5126, "count": 115, "max": [0.9982846975326538, 0.9999515414237976, 0.9995531439781189], "min": [-0.997615396976471, -0.9997968077659607, -0.06111481413245201], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2240296, "componentType": 5126, "count": 115, "max": [0.19426999986171722, 0.1958799958229065], "min": [0.0008880000677891076, 0.0013620257377624512], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5886384, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6655104, "componentType": 5126, "count": 789, "max": [-0.014063634909689426, -0.01615598425269127, 0.4572244882583618], "min": [-0.22451984882354736, -1.199746012687683, 0.24184922873973846], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6664572, "componentType": 5126, "count": 789, "max": [0.988248348236084, 0.9841212034225464, 0.9624184966087341], "min": [-0.9955924153327942, -0.9907649755477905, -0.9977575540542603], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2241216, "componentType": 5126, "count": 789, "max": [0.42956000566482544, 2.7254550457000732], "min": [-1.1633970737457275, -0.8819609880447388], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5888880, "componentType": 5125, "count": 4626, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6674040, "componentType": 5126, "count": 1069, "max": [-0.13595734536647797, -0.09165193885564804, 0.29831111431121826], "min": [-0.26138442754745483, -0.9775084257125854, -0.22343960404396057], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6686868, "componentType": 5126, "count": 1069, "max": [0.2402130663394928, 0.9999987483024597, 0.9999944567680359], "min": [-0.999995231628418, -0.9996983408927917, -0.9999967217445374], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2247528, "componentType": 5126, "count": 1069, "max": [5.371909141540527, 3.2401340007781982], "min": [-3.6754770278930664, -5.104167938232422], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5907384, "componentType": 5125, "count": 5946, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6699696, "componentType": 5126, "count": 3346, "max": [-0.10820180177688599, -0.17009173333644867, 0.2850644886493683], "min": [-0.25640279054641724, -0.9722708463668823, -0.0305352620780468], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6739848, "componentType": 5126, "count": 3346, "max": [0.9998789429664612, 1.0, 0.9999688863754272], "min": [-0.9999921917915344, -0.999850869178772, -0.999755322933197], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2256080, "componentType": 5126, "count": 3346, "max": [3.413830041885376, 6.35478401184082], "min": [-4.46377420425415, -5.059726238250732], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5931168, "componentType": 5125, "count": 18708, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6780000, "componentType": 5126, "count": 1863, "max": [-0.06485544890165329, -0.08100076764822006, 0.4124729335308075], "min": [-0.264443039894104, -1.0096286535263062, -0.26374876499176025], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6802356, "componentType": 5126, "count": 1863, "max": [0.9923388361930847, 0.999756395816803, 0.9998604655265808], "min": [-0.9993623495101929, -0.9997415542602539, -0.9999935626983643], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2282848, "componentType": 5126, "count": 1863, "max": [3.401458978652954, 2.7488579750061035], "min": [-1.4671390056610107, -0.16361093521118164], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6006000, "componentType": 5125, "count": 10470, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6824712, "componentType": 5126, "count": 119, "max": [-0.21485155820846558, -0.25794026255607605, 0.22179214656352997], "min": [-0.22893020510673523, -0.36319348216056824, 0.16570009291172028], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6826140, "componentType": 5126, "count": 119, "max": [0.9578977823257446, 0.969002902507782, 0.998171865940094], "min": [-0.9992680549621582, -0.9504942297935486, -0.9693968296051025], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2297752, "componentType": 5126, "count": 119, "max": [0.31431400775909424, -0.420132040977478], "min": [-0.37035101652145386, -1.2499299049377441], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6047880, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6827568, "componentType": 5126, "count": 275, "max": [-0.20751534402370453, -0.5707256197929382, 0.04912133142352104], "min": [-0.23185092210769653, -0.6043902039527893, 0.04070303961634636], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6830868, "componentType": 5126, "count": 275, "max": [0.9983596801757812, 0.9995981454849243, 0.9998334050178528], "min": [-0.9997014403343201, -0.9999383687973022, -0.07117389142513275], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2298704, "componentType": 5126, "count": 275, "max": [737.822509765625, -337.03021240234375], "min": [716.0662841796875, -368.13299560546875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6050376, "componentType": 5125, "count": 1584, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6834168, "componentType": 5126, "count": 864, "max": [-0.09526151418685913, -0.12917359173297882, 0.7870411276817322], "min": [-0.3262218236923218, -1.106730580329895, 0.3485170304775238], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6844536, "componentType": 5126, "count": 864, "max": [0.9537599086761475, 0.7203285098075867, 0.9793212413787842], "min": [-0.9773598313331604, -0.7750660181045532, -0.9117740988731384], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2300904, "componentType": 5126, "count": 864, "max": [835.1527099609375, 71.75076293945312], "min": [631.640380859375, -830.1400146484375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6056712, "componentType": 5125, "count": 5172, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6854904, "componentType": 5126, "count": 3617, "max": [0.04727661609649658, 0.043470337986946106, 0.43355780839920044], "min": [-0.20150727033615112, -1.2384306192398071, -0.409845232963562], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6898308, "componentType": 5126, "count": 3617, "max": [0.9999977350234985, 0.999889075756073, 0.9913527965545654], "min": [-0.3884371221065521, -0.9990864396095276, -0.9980074763298035], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2307816, "componentType": 5126, "count": 3617, "max": [0.7632579803466797, 0.5660610198974609], "min": [0.6101800203323364, 0.3173220157623291], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6077400, "componentType": 5125, "count": 18402, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6941712, "componentType": 5126, "count": 2000, "max": [0.04001665115356445, 0.04337833821773529, 0.4311148226261139], "min": [-0.202090322971344, -1.238310694694519, -0.41281622648239136], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6965712, "componentType": 5126, "count": 2000, "max": [0.12122248858213425, 0.9999961853027344, 0.9978253841400146], "min": [-0.9999992251396179, -0.9860734939575195, -0.9999996423721313], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2336752, "componentType": 5126, "count": 2000, "max": [0.7868719696998596, 0.6772210597991943], "min": [0.00022700001136399806, 0.0002269744873046875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6151008, "componentType": 5125, "count": 9168, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6989712, "componentType": 5126, "count": 3, "max": [-0.04462629556655884, -0.1446020007133484, 0.2805211544036865], "min": [-0.048309326171875, -0.17701101303100586, 0.2746050953865051], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6989748, "componentType": 5126, "count": 3, "max": [-0.8468214273452759, -0.016562843695282936, -0.5092231035232544], "min": [-0.8604753017425537, -0.01959286257624626, -0.5315164923667908], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2352752, "componentType": 5126, "count": 3, "max": [0.015709001570940018, 0.1520569920539856], "min": [0.009048000909388065, 0.1464729905128479], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6187680, "componentType": 5125, "count": 3, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6989784, "componentType": 5126, "count": 950, "max": [0.8432796597480774, 0.8114318251609802, 0.16276158392429352], "min": [-0.842756450176239, -0.48535847663879395, -0.400107741355896], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7001184, "componentType": 5126, "count": 950, "max": [0.9409210681915283, 0.999886691570282, 0.9742619395256042], "min": [-0.9410884380340576, -0.8726921677589417, -0.9999536275863647], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2352776, "componentType": 5126, "count": 950, "max": [753.6464233398438, 1734.6080322265625], "min": [-753.6464233398438, 538.4500122070312], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6187692, "componentType": 5125, "count": 5688, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7012584, "componentType": 5126, "count": 3867, "max": [0.11257106065750122, 0.6689918041229248, -0.3952186107635498], "min": [-1.8588227033615112, 0.3260577321052551, -0.6456900835037231], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7058988, "componentType": 5126, "count": 3867, "max": [1.0, 0.9999951124191284, 0.9999989867210388], "min": [-1.0, -0.6099360585212708, -0.9999962449073792], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2360376, "componentType": 5126, "count": 3867, "max": [881.1989135742188, 2712.16796875], "min": [0.0002440000098431483, 2395.7451171875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6210444, "componentType": 5125, "count": 20178, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7105392, "componentType": 5126, "count": 150, "max": [0.05698080360889435, 0.4063941240310669, -0.4107828140258789], "min": [-1.8032329082489014, 0.38375619053840637, -0.430604487657547], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7107192, "componentType": 5126, "count": 150, "max": [0.6821222901344299, 0.7312642335891724, -0.07901258021593094], "min": [-0.6820608973503113, 0.7079070210456848, -0.2297985702753067], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2391312, "componentType": 5126, "count": 150, "max": [0.9593450427055359, 0.3124120235443115], "min": [0.9201760292053223, 0.23497998714447021], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6291156, "componentType": 5125, "count": 720, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7108992, "componentType": 5126, "count": 208, "max": [0.027654549106955528, 0.5488877296447754, -0.41865065693855286], "min": [-0.19392430782318115, 0.40676596760749817, -0.44018352031707764], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7111488, "componentType": 5126, "count": 208, "max": [0.6315382719039917, 0.8785167932510376, 0.9988610148429871], "min": [-0.6315336227416992, -0.8784701228141785, -0.9988650679588318], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2392512, "componentType": 5126, "count": 208, "max": [0.19035589694976807, 0.6844397783279419], "min": [0.007206309586763382, 0.6664168834686279], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6294036, "componentType": 5125, "count": 1152, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7113984, "componentType": 5126, "count": 208, "max": [-1.5523263216018677, 0.5488877296447754, -0.41865065693855286], "min": [-1.773905873298645, 0.40676596760749817, -0.44018352031707764], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7116480, "componentType": 5126, "count": 208, "max": [0.6315358281135559, 0.8785203695297241, 0.9988617300987244], "min": [-0.6315448880195618, -0.8784686923027039, -0.998865008354187], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2394176, "componentType": 5126, "count": 208, "max": [0.19035589694976807, 0.6844397783279419], "min": [0.007206309586763382, 0.6664168834686279], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6298644, "componentType": 5125, "count": 1152, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7118976, "componentType": 5126, "count": 308, "max": [0.06520244479179382, 0.41799503564834595, -0.40780696272850037], "min": [0.027288386598229408, 0.3875076174736023, -0.4333511292934418], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7122672, "componentType": 5126, "count": 308, "max": [0.9920369982719421, 0.9975813627243042, 0.9996382594108582], "min": [-0.8762840032577515, -0.5678181648254395, -0.9992503523826599], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2395840, "componentType": 5126, "count": 308, "max": [0.062414467334747314, 0.642134964466095], "min": [0.009291641414165497, 0.6058922410011292], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2398304, "componentType": 5126, "count": 308, "max": [0.062414467334747314, 0.642134964466095], "min": [0.009291641414165497, 0.6058922410011292], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6303252, "componentType": 5125, "count": 1740, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7126368, "componentType": 5126, "count": 308, "max": [-1.7735401391983032, 0.41799503564834595, -0.40780696272850037], "min": [-1.811454176902771, 0.3875076174736023, -0.4333511292934418], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7130064, "componentType": 5126, "count": 308, "max": [0.8762933015823364, 0.9975641369819641, 0.9996384978294373], "min": [-0.9920427203178406, -0.5678172707557678, -0.9992518424987793], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2400768, "componentType": 5126, "count": 308, "max": [0.062378592789173126, 0.642134964466095], "min": [0.00925612822175026, 0.6058922410011292], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2403232, "componentType": 5126, "count": 308, "max": [0.062378592789173126, 0.642134964466095], "min": [0.00925612822175026, 0.6058922410011292], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6310212, "componentType": 5125, "count": 1740, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7133760, "componentType": 5126, "count": 3078, "max": [0.10427801311016083, 0.5758237838745117, 0.008457624353468418], "min": [-1.8505297899246216, 0.03675803542137146, -0.451309472322464], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7170696, "componentType": 5126, "count": 3078, "max": [0.9994989633560181, 0.9778357744216919, 0.9988494515419006], "min": [-0.9994956254959106, -0.8560693264007568, -0.9987167716026306], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2405696, "componentType": 5126, "count": 3078, "max": [873.7855224609375, 2626.423095703125], "min": [467.6877136230469, 2129.631103515625], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6317172, "componentType": 5125, "count": 17304, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7207632, "componentType": 5126, "count": 9972, "max": [0.17858725786209106, 0.7073789834976196, -0.14757391810417175], "min": [-1.924793004989624, 0.051319003105163574, -0.7196359634399414], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7327296, "componentType": 5126, "count": 9972, "max": [0.999682605266571, 0.9999910593032837, 0.9993082284927368], "min": [-0.9997032284736633, -0.9999898076057434, -0.9999951124191284], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2430320, "componentType": 5126, "count": 9972, "max": [0.7624069452285767, 0.31668800115585327], "min": [0.3255450129508972, 0.201990008354187], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6386388, "componentType": 5125, "count": 48408, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7446960, "componentType": 5126, "count": 1440, "max": [-0.0013577808858826756, -0.3900858461856842, -0.5957050323486328], "min": [-1.7447963953018188, -0.5853597521781921, -0.6375104784965515], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7464240, "componentType": 5126, "count": 1440, "max": [0.9618785977363586, 0.4494471251964569, 0.999198853969574], "min": [-0.9618205428123474, -0.9573087096214294, -0.998202919960022], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2510096, "componentType": 5126, "count": 1440, "max": [0.6188010573387146, 0.8129809498786926], "min": [0.36273902654647827, 0.3807160258293152], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6580020, "componentType": 5125, "count": 7680, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7481520, "componentType": 5126, "count": 9540, "max": [0.21846473217010498, 0.14885008335113525, -0.1600291132926941], "min": [-1.9646751880645752, -0.690889835357666, -0.8197770118713379], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7596000, "componentType": 5126, "count": 9540, "max": [0.9997259974479675, 0.9999995827674866, 0.9999944567680359], "min": [-0.9997207522392273, -0.9999940991401672, -0.999995768070221], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2521616, "componentType": 5126, "count": 9540, "max": [0.6561490297317505, 0.7171280384063721], "min": [0.16293399035930634, 0.5922589898109436], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6610740, "componentType": 5125, "count": 46716, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7710480, "componentType": 5126, "count": 338, "max": [-0.00917388591915369, -0.39023736119270325, -0.5977783799171448], "min": [-1.7369801998138428, -0.5796752572059631, -0.6358450055122375], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7714536, "componentType": 5126, "count": 338, "max": [0.7544845938682556, -0.6492722630500793, 0.147568017244339], "min": [-0.7544690370559692, -0.940125584602356, -0.001954398350790143], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2597936, "componentType": 5126, "count": 338, "max": [97.1171875, 5.361328125], "min": [-68.6766357421875, -8.02593994140625], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2600640, "componentType": 5126, "count": 338, "max": [97.1171875, 5.361328125], "min": [-68.6766357421875, -8.02593994140625], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6797604, "componentType": 5125, "count": 1776, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7718592, "componentType": 5126, "count": 1574, "max": [0.060779355466365814, -0.0188602264970541, 0.020388903096318245], "min": [-1.806933045387268, -0.4825478494167328, -0.1689242124557495], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7737480, "componentType": 5126, "count": 1574, "max": [0.9981797933578491, 0.8016706705093384, 0.9952722787857056], "min": [-0.9981812834739685, -0.9933621287345886, -0.9113922119140625], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2603344, "componentType": 5126, "count": 1574, "max": [0.0, 1.0], "min": [0.0, 1.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6804708, "componentType": 5125, "count": 8976, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7756368, "componentType": 5126, "count": 2651, "max": [0.8409460186958313, 1.2276464700698853, 0.005420997738838196], "min": [-0.840423047542572, 0.006916403770446777, -0.30548495054244995], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7788180, "componentType": 5126, "count": 2651, "max": [0.993037760257721, 0.9950289130210876, 0.9981071949005127], "min": [-0.9930245876312256, -0.9557604789733887, -0.30516722798347473], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2615936, "componentType": 5126, "count": 2651, "max": [0.6053100228309631, 0.20135599374771118], "min": [0.3255450129508972, 0.00031697750091552734], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6840612, "componentType": 5125, "count": 13716, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7819992, "componentType": 5126, "count": 2026, "max": [0.053607091307640076, 1.1811262369155884, -0.21884813904762268], "min": [-0.05309323966503143, 1.0912084579467773, -0.2703940272331238], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7844304, "componentType": 5126, "count": 2026, "max": [0.9889780282974243, 0.9900675415992737, 0.9906718730926514], "min": [-0.9886423349380493, -0.8733968734741211, -0.48906993865966797], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2637144, "componentType": 5126, "count": 2026, "max": [0.4497630000114441, 0.6155270338058472], "min": [0.36273902654647827, 0.13284802436828613], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6895476, "componentType": 5125, "count": 11244, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7868616, "componentType": 5126, "count": 2160, "max": [0.15052907168865204, 0.34051254391670227, 0.3403853178024292], "min": [-2.000533103942871, -0.3401946425437927, -0.3403117060661316], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7894536, "componentType": 5126, "count": 2160, "max": [0.9997228384017944, 0.8745397329330444, 0.8745200634002686], "min": [-0.9997231364250183, -0.8745432496070862, -0.8745071887969971], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2653352, "componentType": 5126, "count": 2160, "max": [0.9951403737068176, 0.9969479441642761], "min": [0.004145139828324318, 0.005952954292297363], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6940452, "componentType": 5125, "count": 11520, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7920456, "componentType": 5126, "count": 578, "max": [0.15358799695968628, 0.29177001118659973, 0.21200188994407654], "min": [-2.003592014312744, 0.008365856483578682, -0.29136863350868225], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7927392, "componentType": 5126, "count": 578, "max": [0.9625696539878845, 0.999969482421875, 0.7286087870597839], "min": [-0.9625712633132935, -0.3931143283843994, -0.9996424317359924], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2670632, "componentType": 5126, "count": 578, "max": [0.3288569450378418, 0.6503955125808716], "min": [-0.1920309066772461, -0.274783730506897], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6986532, "componentType": 5125, "count": 1614, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7934328, "componentType": 5126, "count": 1138, "max": [0.12714822590351105, 0.077712282538414, 0.07791654765605927], "min": [-1.9771523475646973, -0.07799112796783447, -0.07805076986551285], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7947984, "componentType": 5126, "count": 1138, "max": [1.0, 1.0, 0.998308002948761], "min": [-1.0, -1.0, -0.9983079433441162], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2675256, "componentType": 5126, "count": 1138, "max": [1.0, 1.0], "min": [0.0, -2.0491929054260254], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6992988, "componentType": 5125, "count": 5892, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7961640, "componentType": 5126, "count": 2618, "max": [0.12757784128189087, 0.0187736377120018, 0.01704307831823826], "min": [-1.9775819778442383, -0.018457641825079918, -0.019804319366812706], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7993056, "componentType": 5126, "count": 2618, "max": [1.0, 0.9856505393981934, 1.0], "min": [-1.0, -0.9907459616661072, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2684360, "componentType": 5126, "count": 2618, "max": [0.378157377243042, 0.29206734895706177], "min": [0.30972743034362793, 0.22434300184249878], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7016556, "componentType": 5125, "count": 14544, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8024472, "componentType": 5126, "count": 1586, "max": [0.14486518502235413, 0.3732994496822357, 0.3731720447540283], "min": [-1.9948692321777344, -0.37298280000686646, -0.3730985224246979], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8043504, "componentType": 5126, "count": 1586, "max": [0.9876168370246887, 0.9993777871131897, 0.9993777275085449], "min": [-0.9876150488853455, -0.9993777871131897, -0.9993777275085449], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2705304, "componentType": 5126, "count": 1586, "max": [1.0019831657409668, 0.9997701644897461], "min": [0.0016993284225463867, -6.4621405601501465], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7074732, "componentType": 5125, "count": 8640, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8062536, "componentType": 5126, "count": 1554, "max": [0.06282082945108414, 0.20518910884857178, 0.20568405091762543], "min": [-1.9128248691558838, -0.20548546314239502, -0.20507478713989258], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8081184, "componentType": 5126, "count": 1554, "max": [0.9999997615814209, 0.9984803199768066, 0.9984799027442932], "min": [-0.9999997615814209, -0.9984804391860962, -0.9984793066978455], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2717992, "componentType": 5126, "count": 1554, "max": [1.0015766620635986, 1.0], "min": [0.0016046911478042603, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7109292, "componentType": 5125, "count": 4536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8099832, "componentType": 5126, "count": 23464, "max": [0.1556379199028015, 0.29176878929138184, 0.2916814386844635], "min": [-2.0056419372558594, -0.2915111780166626, -0.291496604681015], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8381400, "componentType": 5126, "count": 23464, "max": [0.9999973773956299, 0.9999982714653015, 0.9999997615814209], "min": [-0.9999972581863403, -0.9999984502792358, -0.9999988079071045], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2730424, "componentType": 5126, "count": 23464, "max": [0.9998960494995117, 0.581977903842926], "min": [0.00010400000610388815, 0.00010401010513305664], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7127436, "componentType": 5125, "count": 112170, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8662968, "componentType": 5126, "count": 2160, "max": [0.15052907168865204, 0.3405126631259918, 0.3403853178024292], "min": [-1.967694878578186, -0.3401946425437927, -0.3403117060661316], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8688888, "componentType": 5126, "count": 2160, "max": [0.9997228384017944, 0.8745441436767578, 0.8745231628417969], "min": [-0.9997217655181885, -0.8745432496070862, -0.8745102882385254], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2918136, "componentType": 5126, "count": 2160, "max": [0.9951403737068176, 0.9969479441642761], "min": [0.004145139828324318, 0.005952954292297363], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7576116, "componentType": 5125, "count": 11520, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8714808, "componentType": 5126, "count": 578, "max": [0.15358799695968628, 0.2917701303958893, 0.21200188994407654], "min": [-1.9707539081573486, 0.008365856483578682, -0.29136863350868225], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8721744, "componentType": 5126, "count": 578, "max": [0.9625696539878845, 0.999969482421875, 0.7286165356636047], "min": [-0.9625716805458069, -0.3931143283843994, -0.9996424317359924], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2935416, "componentType": 5126, "count": 578, "max": [0.3288569450378418, 0.6503955125808716], "min": [-0.1920309066772461, -0.274783730506897], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7622196, "componentType": 5125, "count": 1614, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8728680, "componentType": 5126, "count": 1138, "max": [0.12714822590351105, 0.07771240174770355, 0.07791654765605927], "min": [-1.9443141222000122, -0.07799112796783447, -0.07805076986551285], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8742336, "componentType": 5126, "count": 1138, "max": [1.0, 1.0, 0.9983081221580505], "min": [-1.0, -1.0, -0.9983079433441162], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2940040, "componentType": 5126, "count": 1138, "max": [1.0, 1.0], "min": [0.0, -2.0491929054260254], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7628652, "componentType": 5125, "count": 5892, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8755992, "componentType": 5126, "count": 2618, "max": [0.12757784128189087, 0.01877375692129135, 0.01704307831823826], "min": [-1.9447436332702637, -0.018457641825079918, -0.019804319366812706], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8787408, "componentType": 5126, "count": 2618, "max": [1.0, 0.9856551885604858, 1.0], "min": [-1.0, -0.9907463788986206, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2949144, "componentType": 5126, "count": 2618, "max": [0.378157377243042, 0.29206734895706177], "min": [0.30972743034362793, 0.22434300184249878], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7652220, "componentType": 5125, "count": 14544, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8818824, "componentType": 5126, "count": 1586, "max": [0.14486518502235413, 0.37329956889152527, 0.3731720447540283], "min": [-1.9620310068130493, -0.37298280000686646, -0.3730985224246979], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8837856, "componentType": 5126, "count": 1586, "max": [0.9876160025596619, 0.9993777871131897, 0.9993777871131897], "min": [-0.9876150488853455, -0.9993777871131897, -0.9993777275085449], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2970088, "componentType": 5126, "count": 1586, "max": [1.0019831657409668, 0.9997701644897461], "min": [0.0016993284225463867, -6.4621405601501465], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7710396, "componentType": 5125, "count": 8640, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8856888, "componentType": 5126, "count": 1554, "max": [0.06282082945108414, 0.20518922805786133, 0.20568405091762543], "min": [-1.8799866437911987, -0.20548546314239502, -0.20507478713989258], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8875536, "componentType": 5126, "count": 1554, "max": [0.9999997615814209, 0.9984803199768066, 0.9984799027442932], "min": [-0.9999997615814209, -0.9984803199768066, -0.9984793066978455], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2982776, "componentType": 5126, "count": 1554, "max": [1.0015766620635986, 1.0], "min": [0.0016046911478042603, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7744956, "componentType": 5125, "count": 4536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8894184, "componentType": 5126, "count": 23464, "max": [0.1556379199028015, 0.2917689085006714, 0.2916814386844635], "min": [-1.9728038311004639, -0.2915111780166626, -0.291496604681015], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9175752, "componentType": 5126, "count": 23464, "max": [0.9999973773956299, 0.9999982714653015, 0.9999997615814209], "min": [-0.9999973773956299, -0.9999984502792358, -0.9999988079071045], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2995208, "componentType": 5126, "count": 23464, "max": [0.9998960494995117, 0.581977903842926], "min": [0.00010400000610388815, 0.00010401010513305664], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7763100, "componentType": 5125, "count": 112170, "type": "SCALAR"}], "asset": {"extras": {"author": "Ameer Studio (https://sketchfab.com/uchiha.321abc)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/tesla-2018-model-3-5ef9b845aaf44203b6d04e2c677e444f", "title": "Tesla 2018 Model 3"}, "generator": "Sketchfab-12.68.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 8211780, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 3182920, "byteOffset": 8211780, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 9457320, "byteOffset": 11394700, "byteStride": 12, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 20852020, "uri": "scene.bin"}], "images": [{"uri": "textures/hub_rb.0_baseColor.png"}, {"uri": "textures/hub_rf.1_baseColor.png"}, {"uri": "textures/dvorright.0_baseColor.png"}, {"uri": "textures/movsteer_1.0.1_baseColor.png"}, {"uri": "textures/movsteer_1.0.0_baseColor.png"}, {"uri": "textures/primary.001_baseColor.png"}, {"uri": "textures/back_chrome_light.0_baseColor.png"}, {"uri": "textures/pantulans.0_baseColor.png"}, {"uri": "textures/right_rear_light_baseColor.png"}, {"uri": "textures/tembus_red.0_baseColor.png"}, {"uri": "textures/belt.0_baseColor.png"}, {"uri": "textures/Putih.0_baseColor.png"}, {"uri": "textures/texture_Buttons.0_baseColor.png"}, {"uri": "textures/LCDs.0_baseColor.png"}, {"uri": "textures/glass.0_baseColor.png"}, {"uri": "textures/glass.1_baseColor.png"}, {"uri": "textures/primary.002_baseColor.png"}, {"uri": "textures/light_pantulan.0_baseColor.png"}, {"uri": "textures/wheels.2_baseColor.png"}, {"uri": "textures/wheels.3_baseColor.png"}, {"uri": "textures/wheels.4_baseColor.png"}, {"uri": "textures/wheels.6_baseColor.png"}], "materials": [{"doubleSided": true, "name": "hub_rb.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "hub_rf.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.6925175796713128}}, {"doubleSided": true, "name": "hub_rf.1", "pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "dvorright.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "movsteer_1.0.1", "pbrMetallicRoughness": {"baseColorTexture": {"index": 3}, "metallicFactor": 0.7191704819753267, "roughnessFactor": 0.4206735007813023}}, {"doubleSided": true, "name": "movsteer_1.0.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 4}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "chassis.0", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "JUST_BLACK.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "primary", "pbrMetallicRoughness": {"baseColorFactor": [0.8193024440314856, 0.8193024440314856, 0.8193024440314856, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.28082951802467326}}, {"doubleSided": true, "name": "primary.001", "pbrMetallicRoughness": {"baseColorTexture": {"index": 5}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "black_lights.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "back_chrome_light.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 6}, "metallicFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 7}, "name": "pantulans.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 7}, "metallicFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 8}, "name": "right_rear_light", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "breaklight_l", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "foglight_r", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "foglight_l", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "right_front_light", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 8}, "name": "left_front_light", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "aluminium_light.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 3}, "metallicFactor": 0.0}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "tembus_red.0", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 0.8164532525886338], "baseColorTexture": {"index": 9}, "metallicFactor": 0.0, "roughnessFactor": 0.7661633017218952}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 8}, "name": "light_night", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 8}, "name": "indicator_lf", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 8}, "name": "indicator_rf", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "hitam.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "Plastic.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "belt.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 10}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "satin_red.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 7}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "suspensi.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 6}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "suspensi.1", "pbrMetallicRoughness": {"baseColorTexture": {"index": 6}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "aluminium2.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 3}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "Putih.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 11}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "Carpet.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 4}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "Carpet_Light.0", "pbrMetallicRoughness": {"baseColorFactor": [0.141176, 0.141176, 0.141176, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "texture_Buttons.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 12}, "metallicFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 13}, "name": "LCDs.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 13}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "Seat_Leather_white.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 11}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "mirror_inside.0", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "roughnessFactor": 0.20178726690136128}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "glass.0", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 0.6218877113620196], "baseColorTexture": {"index": 14}, "metallicFactor": 0.0, "roughnessFactor": 0.22002778639135645}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "glass.1", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 0.6644489235053415], "baseColorTexture": {"index": 15}, "metallicFactor": 0.0, "roughnessFactor": 0.2625889985346782}}, {"doubleSided": true, "name": "platnomor.1", "pbrMetallicRoughness": {"baseColorFactor": [0.639216, 0.639216, 0.639216, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "platnomor.2", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "indicator_rr", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "indicator_lr", "pbrMetallicRoughness": {"baseColorFactor": [0.709804, 1.0, 0.0, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "left_rear_light", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "revlight_L", "pbrMetallicRoughness": {"baseColorTexture": {"index": 8}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "door_lf.0", "pbrMetallicRoughness": {"baseColorFactor": [0.0745098, 0.0745098, 0.0745098, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "door_lf.5", "pbrMetallicRoughness": {"baseColorFactor": [0.0431373, 0.0431373, 0.0431373, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "primary.002", "pbrMetallicRoughness": {"baseColorTexture": {"index": 16}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "primary.004", "pbrMetallicRoughness": {"baseColorFactor": [0.235294, 1.0, 0.0, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "front_black.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "metallicFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 17}, "name": "light_pantulan.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 17}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "wheels.2", "pbrMetallicRoughness": {"baseColorTexture": {"index": 18}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "wheels.0", "pbrMetallicRoughness": {"baseColorTexture": {"index": 3}, "metallicFactor": 0.0, "roughnessFactor": 0.8235020314518136}}, {"doubleSided": true, "name": "wheels.1", "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "wheels.3", "pbrMetallicRoughness": {"baseColorTexture": {"index": 19}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "wheels.4", "pbrMetallicRoughness": {"baseColorTexture": {"index": 20}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "wheels.6", "pbrMetallicRoughness": {"baseColorTexture": {"index": 21}, "metallicFactor": 0.0, "roughnessFactor": 0.683772233983162}}], "meshes": [{"name": "hub_rb_hub_rb.0_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "hub_lb_hub_rb.0_0", "primitives": [{"attributes": {"NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6}, "indices": 7, "material": 0, "mode": 4}]}, {"name": "hub_rf_hub_rf.0_0", "primitives": [{"attributes": {"NORMAL": 9, "POSITION": 8, "TEXCOORD_0": 10}, "indices": 11, "material": 1, "mode": 4}]}, {"name": "hub_rf_hub_rf.1_0", "primitives": [{"attributes": {"NORMAL": 13, "POSITION": 12, "TEXCOORD_0": 14}, "indices": 15, "material": 2, "mode": 4}]}, {"name": "hub_lf_hub_rf.0_0", "primitives": [{"attributes": {"NORMAL": 17, "POSITION": 16, "TEXCOORD_0": 18}, "indices": 19, "material": 1, "mode": 4}]}, {"name": "hub_lf_hub_rf.1_0", "primitives": [{"attributes": {"NORMAL": 21, "POSITION": 20, "TEXCOORD_0": 22}, "indices": 23, "material": 2, "mode": 4}]}, {"name": "dvorright_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24, "TEXCOORD_0": 26}, "indices": 27, "material": 3, "mode": 4}]}, {"name": "dvorleft_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 29, "POSITION": 28, "TEXCOORD_0": 30}, "indices": 31, "material": 3, "mode": 4}]}, {"name": "movsteer_1.0_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 33, "POSITION": 32, "TEXCOORD_0": 34}, "indices": 35, "material": 4, "mode": 4}]}, {"name": "movsteer_1.0_movsteer_1.0.0_0", "primitives": [{"attributes": {"NORMAL": 37, "POSITION": 36, "TEXCOORD_0": 38}, "indices": 39, "material": 5, "mode": 4}]}, {"name": "movsteer_1.0_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 41, "POSITION": 40, "TEXCOORD_0": 42}, "indices": 43, "material": 3, "mode": 4}]}, {"name": "chassis_chassis.0_0", "primitives": [{"attributes": {"NORMAL": 45, "POSITION": 44, "TEXCOORD_0": 46, "TEXCOORD_1": 47}, "indices": 48, "material": 6, "mode": 4}]}, {"name": "JUST_BLACK_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 50, "POSITION": 49, "TEXCOORD_0": 51}, "indices": 52, "material": 7, "mode": 4}]}, {"name": "JUST_BLACK.001_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 54, "POSITION": 53, "TEXCOORD_0": 55}, "indices": 56, "material": 7, "mode": 4}]}, {"name": "body_primary_0", "primitives": [{"attributes": {"NORMAL": 58, "POSITION": 57, "TEXCOORD_0": 59}, "indices": 60, "material": 8, "mode": 4}]}, {"name": "bodysills_primary.001_0", "primitives": [{"attributes": {"NORMAL": 62, "POSITION": 61, "TEXCOORD_0": 63}, "indices": 64, "material": 9, "mode": 4}]}, {"name": "black_lights_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 66, "POSITION": 65, "TEXCOORD_0": 67}, "indices": 68, "material": 10, "mode": 4}]}, {"name": "_satin_black_134_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 70, "POSITION": 69, "TEXCOORD_0": 71}, "indices": 72, "material": 10, "mode": 4}]}, {"name": "_plastic_black_124_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 74, "POSITION": 73, "TEXCOORD_0": 75}, "indices": 76, "material": 10, "mode": 4}]}, {"name": "back_chrome_light_back_chrome_light.0_0", "primitives": [{"attributes": {"NORMAL": 78, "POSITION": 77, "TEXCOORD_0": 79}, "indices": 80, "material": 11, "mode": 4}]}, {"name": "pantulans_pantulans.0_0", "primitives": [{"attributes": {"NORMAL": 82, "POSITION": 81, "TEXCOORD_0": 83, "TEXCOORD_1": 84}, "indices": 85, "material": 12, "mode": 4}]}, {"name": "rear_lights_right rear light_0", "primitives": [{"attributes": {"NORMAL": 87, "POSITION": 86, "TEXCOORD_0": 88, "TEXCOORD_1": 89}, "indices": 90, "material": 13, "mode": 4}]}, {"name": "light_breake_breaklight l_0", "primitives": [{"attributes": {"NORMAL": 92, "POSITION": 91, "TEXCOORD_0": 93}, "indices": 94, "material": 14, "mode": 4}]}, {"name": "chrome_foglight_r_foglight r_0", "primitives": [{"attributes": {"NORMAL": 96, "POSITION": 95, "TEXCOORD_0": 97}, "indices": 98, "material": 15, "mode": 4}]}, {"name": "chrome_foglight_l_foglight l_0", "primitives": [{"attributes": {"NORMAL": 100, "POSITION": 99, "TEXCOORD_0": 101}, "indices": 102, "material": 16, "mode": 4}]}, {"name": "chrome_Lights_head_l_right front light_0", "primitives": [{"attributes": {"NORMAL": 104, "POSITION": 103, "TEXCOORD_0": 105}, "indices": 106, "material": 17, "mode": 4}]}, {"name": "chrome_Lights_head_l_left front light_0", "primitives": [{"attributes": {"NORMAL": 108, "POSITION": 107, "TEXCOORD_0": 109, "TEXCOORD_1": 110}, "indices": 111, "material": 18, "mode": 4}]}, {"name": "chrome_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 113, "POSITION": 112, "TEXCOORD_0": 114}, "indices": 115, "material": 4, "mode": 4}]}, {"name": "breake_int_breaklight l_0", "primitives": [{"attributes": {"NORMAL": 117, "POSITION": 116, "TEXCOORD_0": 118}, "indices": 119, "material": 14, "mode": 4}]}, {"name": "aluminium_light_aluminium_light.0_0", "primitives": [{"attributes": {"NORMAL": 121, "POSITION": 120, "TEXCOORD_0": 122}, "indices": 123, "material": 19, "mode": 4}]}, {"name": "tembus red_tembus red.0_0", "primitives": [{"attributes": {"NORMAL": 125, "POSITION": 124, "TEXCOORD_0": 126}, "indices": 127, "material": 20, "mode": 4}]}, {"name": "interiorlights_light night_0", "primitives": [{"attributes": {"NORMAL": 129, "POSITION": 128, "TEXCOORD_0": 130, "TEXCOORD_1": 131}, "indices": 132, "material": 21, "mode": 4}]}, {"name": "turn_indicat_l_indicator lf_0", "primitives": [{"attributes": {"NORMAL": 134, "POSITION": 133, "TEXCOORD_0": 135, "TEXCOORD_1": 136}, "indices": 137, "material": 22, "mode": 4}]}, {"name": "turn_indicat_r_indicator rf_0", "primitives": [{"attributes": {"NORMAL": 139, "POSITION": 138, "TEXCOORD_0": 140, "TEXCOORD_1": 141}, "indices": 142, "material": 23, "mode": 4}]}, {"name": "base_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 144, "POSITION": 143, "TEXCOORD_0": 145}, "indices": 146, "material": 3, "mode": 4}]}, {"name": "hitam_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 148, "POSITION": 147, "TEXCOORD_0": 149}, "indices": 150, "material": 3, "mode": 4}]}, {"name": "hitam.001_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 152, "POSITION": 151, "TEXCOORD_0": 153}, "indices": 154, "material": 3, "mode": 4}]}, {"name": "hitam.002_hitam.0_0", "primitives": [{"attributes": {"NORMAL": 156, "POSITION": 155, "TEXCOORD_0": 157}, "indices": 158, "material": 24, "mode": 4}]}, {"name": "Plastic_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 160, "POSITION": 159, "TEXCOORD_0": 161}, "indices": 162, "material": 25, "mode": 4}]}, {"name": "belt_belt.0_0", "primitives": [{"attributes": {"NORMAL": 164, "POSITION": 163, "TEXCOORD_0": 165}, "indices": 166, "material": 26, "mode": 4}]}, {"name": "black_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 168, "POSITION": 167, "TEXCOORD_0": 169}, "indices": 170, "material": 10, "mode": 4}]}, {"name": "satin_red_satin_red.0_0", "primitives": [{"attributes": {"NORMAL": 172, "POSITION": 171, "TEXCOORD_0": 173}, "indices": 174, "material": 27, "mode": 4}]}, {"name": "paint_black_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 176, "POSITION": 175, "TEXCOORD_0": 177}, "indices": 178, "material": 3, "mode": 4}]}, {"name": "cahrome_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 180, "POSITION": 179, "TEXCOORD_0": 181}, "indices": 182, "material": 4, "mode": 4}]}, {"name": "suspensi_suspensi.0_0", "primitives": [{"attributes": {"NORMAL": 184, "POSITION": 183, "TEXCOORD_0": 185}, "indices": 186, "material": 28, "mode": 4}]}, {"name": "suspensi_suspensi.1_0", "primitives": [{"attributes": {"NORMAL": 188, "POSITION": 187, "TEXCOORD_0": 189}, "indices": 190, "material": 29, "mode": 4}]}, {"name": "chrome__movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 192, "POSITION": 191, "TEXCOORD_0": 193}, "indices": 194, "material": 4, "mode": 4}]}, {"name": "chrome1_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 196, "POSITION": 195, "TEXCOORD_0": 197}, "indices": 198, "material": 4, "mode": 4}]}, {"name": "chrome2_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 200, "POSITION": 199, "TEXCOORD_0": 201}, "indices": 202, "material": 4, "mode": 4}]}, {"name": "hitam.003_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 204, "POSITION": 203, "TEXCOORD_0": 205}, "indices": 206, "material": 3, "mode": 4}]}, {"name": "hitam.004_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 208, "POSITION": 207, "TEXCOORD_0": 209}, "indices": 210, "material": 3, "mode": 4}]}, {"name": "aluminium_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 212, "POSITION": 211, "TEXCOORD_0": 213}, "indices": 214, "material": 4, "mode": 4}]}, {"name": "hitam.005_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 216, "POSITION": 215, "TEXCOORD_0": 217}, "indices": 218, "material": 25, "mode": 4}]}, {"name": "hitam.006_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 220, "POSITION": 219, "TEXCOORD_0": 221}, "indices": 222, "material": 10, "mode": 4}]}, {"name": "texture_Leather_movsteer_1.0.0_0", "primitives": [{"attributes": {"NORMAL": 224, "POSITION": 223, "TEXCOORD_0": 225}, "indices": 226, "material": 5, "mode": 4}]}, {"name": "texture_Leather.001_movsteer_1.0.0_0", "primitives": [{"attributes": {"NORMAL": 228, "POSITION": 227, "TEXCOORD_0": 229}, "indices": 230, "material": 5, "mode": 4}]}, {"name": "aluminium2_aluminium2.0_0", "primitives": [{"attributes": {"NORMAL": 232, "POSITION": 231, "TEXCOORD_0": 233}, "indices": 234, "material": 30, "mode": 4}]}, {"name": "frunkplastic_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 236, "POSITION": 235, "TEXCOORD_0": 237}, "indices": 238, "material": 3, "mode": 4}]}, {"name": "Putih_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 240, "POSITION": 239, "TEXCOORD_0": 241}, "indices": 242, "material": 31, "mode": 4}]}, {"name": "whiteleather_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 244, "POSITION": 243, "TEXCOORD_0": 245}, "indices": 246, "material": 31, "mode": 4}]}, {"name": "Putih.001_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 248, "POSITION": 247, "TEXCOORD_0": 249}, "indices": 250, "material": 31, "mode": 4}]}, {"name": "Putih.002_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 252, "POSITION": 251, "TEXCOORD_0": 253}, "indices": 254, "material": 31, "mode": 4}]}, {"name": "Carpet_Carpet.0_0", "primitives": [{"attributes": {"NORMAL": 256, "POSITION": 255, "TEXCOORD_0": 257}, "indices": 258, "material": 32, "mode": 4}]}, {"name": "black.001_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 260, "POSITION": 259, "TEXCOORD_0": 261}, "indices": 262, "material": 25, "mode": 4}]}, {"name": "black.002_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 264, "POSITION": 263, "TEXCOORD_0": 265}, "indices": 266, "material": 25, "mode": 4}]}, {"name": "black.003_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 268, "POSITION": 267, "TEXCOORD_0": 269}, "indices": 270, "material": 10, "mode": 4}]}, {"name": "black.004_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 272, "POSITION": 271, "TEXCOORD_0": 273}, "indices": 274, "material": 7, "mode": 4}]}, {"name": "black.005_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 276, "POSITION": 275, "TEXCOORD_0": 277}, "indices": 278, "material": 10, "mode": 4}]}, {"name": "Carpet_Light_Carpet_Light.0_0", "primitives": [{"attributes": {"NORMAL": 280, "POSITION": 279, "TEXCOORD_0": 281}, "indices": 282, "material": 33, "mode": 4}]}, {"name": "chromeBELT_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 284, "POSITION": 283, "TEXCOORD_0": 285}, "indices": 286, "material": 4, "mode": 4}]}, {"name": "suspensi2_suspensi.0_0", "primitives": [{"attributes": {"NORMAL": 288, "POSITION": 287, "TEXCOORD_0": 289}, "indices": 290, "material": 28, "mode": 4}]}, {"name": "suspensi2_suspensi.1_0", "primitives": [{"attributes": {"NORMAL": 292, "POSITION": 291, "TEXCOORD_0": 293}, "indices": 294, "material": 29, "mode": 4}]}, {"name": "texture_Buttons_texture_Buttons.0_0", "primitives": [{"attributes": {"NORMAL": 296, "POSITION": 295, "TEXCOORD_0": 297}, "indices": 298, "material": 34, "mode": 4}]}, {"name": "LCDs_LCDs.0_0", "primitives": [{"attributes": {"NORMAL": 300, "POSITION": 299, "TEXCOORD_0": 301, "TEXCOORD_1": 302}, "indices": 303, "material": 35, "mode": 4}]}, {"name": "<PERSON><PERSON> Leather white_<PERSON><PERSON> Leather white.0_0", "primitives": [{"attributes": {"NORMAL": 305, "POSITION": 304, "TEXCOORD_0": 306}, "indices": 307, "material": 36, "mode": 4}]}, {"name": "<PERSON><PERSON>_white_<PERSON><PERSON> Leather white.0_0", "primitives": [{"attributes": {"NORMAL": 309, "POSITION": 308, "TEXCOORD_0": 310}, "indices": 311, "material": 36, "mode": 4}]}, {"name": "mirror_inside_mirror_inside.0_0", "primitives": [{"attributes": {"NORMAL": 313, "POSITION": 312, "TEXCOORD_0": 314}, "indices": 315, "material": 37, "mode": 4}]}, {"name": "glass_glass.0_0", "primitives": [{"attributes": {"NORMAL": 317, "POSITION": 316, "TEXCOORD_0": 318}, "indices": 319, "material": 38, "mode": 4}]}, {"name": "glass_glass.1_0", "primitives": [{"attributes": {"NORMAL": 321, "POSITION": 320, "TEXCOORD_0": 322}, "indices": 323, "material": 39, "mode": 4}]}, {"name": "black_boot_black_lights.0_0", "primitives": [{"attributes": {"NORMAL": 325, "POSITION": 324, "TEXCOORD_0": 326}, "indices": 327, "material": 10, "mode": 4}]}, {"name": "boot_primary_0", "primitives": [{"attributes": {"NORMAL": 329, "POSITION": 328, "TEXCOORD_0": 330}, "indices": 331, "material": 8, "mode": 4}]}, {"name": "platnomor_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 333, "POSITION": 332, "TEXCOORD_0": 334}, "indices": 335, "material": 7, "mode": 4}]}, {"name": "platnomor_platnomor.1_0", "primitives": [{"attributes": {"NORMAL": 337, "POSITION": 336, "TEXCOORD_0": 338}, "indices": 339, "material": 40, "mode": 4}]}, {"name": "platnomor_hitam.0_0", "primitives": [{"attributes": {"NORMAL": 341, "POSITION": 340, "TEXCOORD_0": 342}, "indices": 343, "material": 24, "mode": 4}]}, {"name": "platnomor_platnomor.2_0", "primitives": [{"attributes": {"NORMAL": 345, "POSITION": 344, "TEXCOORD_0": 346}, "indices": 347, "material": 41, "mode": 4}]}, {"name": "chrome.001_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 349, "POSITION": 348, "TEXCOORD_0": 350}, "indices": 351, "material": 4, "mode": 4}]}, {"name": "light_turn_rr_boot_indicator rr_0", "primitives": [{"attributes": {"NORMAL": 353, "POSITION": 352, "TEXCOORD_0": 354}, "indices": 355, "material": 42, "mode": 4}]}, {"name": "light_turn_lr_boot_indicator lr_0", "primitives": [{"attributes": {"NORMAL": 357, "POSITION": 356, "TEXCOORD_0": 358}, "indices": 359, "material": 43, "mode": 4}]}, {"name": "chrome_light_back_chrome_light.0_0", "primitives": [{"attributes": {"NORMAL": 361, "POSITION": 360, "TEXCOORD_0": 362}, "indices": 363, "material": 11, "mode": 4}]}, {"name": "rear_lightsr_right rear light_0", "primitives": [{"attributes": {"NORMAL": 365, "POSITION": 364, "TEXCOORD_0": 366, "TEXCOORD_1": 367}, "indices": 368, "material": 13, "mode": 4}]}, {"name": "rear_lightsl_left rear light_0", "primitives": [{"attributes": {"NORMAL": 370, "POSITION": 369, "TEXCOORD_0": 371}, "indices": 372, "material": 44, "mode": 4}]}, {"name": "lightrevese_boot_revlight L_0", "primitives": [{"attributes": {"NORMAL": 374, "POSITION": 373, "TEXCOORD_0": 375}, "indices": 376, "material": 45, "mode": 4}]}, {"name": "tembus_boot_ok_tembus red.0_0", "primitives": [{"attributes": {"NORMAL": 378, "POSITION": 377, "TEXCOORD_0": 379}, "indices": 380, "material": 20, "mode": 4}]}, {"name": "door_lf_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 382, "POSITION": 381, "TEXCOORD_0": 383}, "indices": 384, "material": 7, "mode": 4}]}, {"name": "door_lf_texture_Buttons.0_0", "primitives": [{"attributes": {"NORMAL": 386, "POSITION": 385, "TEXCOORD_0": 387}, "indices": 388, "material": 34, "mode": 4}]}, {"name": "door_lf_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 390, "POSITION": 389, "TEXCOORD_0": 391}, "indices": 392, "material": 4, "mode": 4}]}, {"name": "door_lf_movsteer_1.0.0_0", "primitives": [{"attributes": {"NORMAL": 394, "POSITION": 393, "TEXCOORD_0": 395}, "indices": 396, "material": 5, "mode": 4}]}, {"name": "door_lf_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 398, "POSITION": 397, "TEXCOORD_0": 399}, "indices": 400, "material": 31, "mode": 4}]}, {"name": "door_lf_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 402, "POSITION": 401, "TEXCOORD_0": 403}, "indices": 404, "material": 25, "mode": 4}]}, {"name": "door_lf_aluminium2.0_0", "primitives": [{"attributes": {"NORMAL": 406, "POSITION": 405, "TEXCOORD_0": 407}, "indices": 408, "material": 30, "mode": 4}]}, {"name": "door_lf_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 410, "POSITION": 409, "TEXCOORD_0": 411}, "indices": 412, "material": 3, "mode": 4}]}, {"name": "door_lf_door_lf.0_0", "primitives": [{"attributes": {"NORMAL": 414, "POSITION": 413, "TEXCOORD_0": 415}, "indices": 416, "material": 46, "mode": 4}]}, {"name": "door_lf_door_lf.5_0", "primitives": [{"attributes": {"NORMAL": 418, "POSITION": 417, "TEXCOORD_0": 419}, "indices": 420, "material": 47, "mode": 4}]}, {"name": "door_lf_glass.0_0", "primitives": [{"attributes": {"NORMAL": 422, "POSITION": 421, "TEXCOORD_0": 423}, "indices": 424, "material": 38, "mode": 4}]}, {"name": "door_lf_mirror_inside.0_0", "primitives": [{"attributes": {"NORMAL": 426, "POSITION": 425, "TEXCOORD_0": 427}, "indices": 428, "material": 37, "mode": 4}]}, {"name": "door_lf_primary_0", "primitives": [{"attributes": {"NORMAL": 430, "POSITION": 429, "TEXCOORD_0": 431}, "indices": 432, "material": 8, "mode": 4}]}, {"name": "door_lf_primary.002_0", "primitives": [{"attributes": {"NORMAL": 434, "POSITION": 433, "TEXCOORD_0": 435}, "indices": 436, "material": 48, "mode": 4}]}, {"name": "door_lf_ok_primary_0", "primitives": [{"attributes": {"NORMAL": 438, "POSITION": 437, "TEXCOORD_0": 439}, "indices": 440, "material": 8, "mode": 4}]}, {"name": "door_lr_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 442, "POSITION": 441, "TEXCOORD_0": 443}, "indices": 444, "material": 7, "mode": 4}]}, {"name": "door_lr_texture_Buttons.0_0", "primitives": [{"attributes": {"NORMAL": 446, "POSITION": 445, "TEXCOORD_0": 447}, "indices": 448, "material": 34, "mode": 4}]}, {"name": "door_lr_primary.004_0", "primitives": [{"attributes": {"NORMAL": 450, "POSITION": 449, "TEXCOORD_0": 451}, "indices": 452, "material": 49, "mode": 4}]}, {"name": "door_lr_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 454, "POSITION": 453, "TEXCOORD_0": 455}, "indices": 456, "material": 4, "mode": 4}]}, {"name": "door_lr_movsteer_1.0.0_0", "primitives": [{"attributes": {"NORMAL": 458, "POSITION": 457, "TEXCOORD_0": 459}, "indices": 460, "material": 5, "mode": 4}]}, {"name": "door_lr_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 462, "POSITION": 461, "TEXCOORD_0": 463}, "indices": 464, "material": 31, "mode": 4}]}, {"name": "door_lr_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 466, "POSITION": 465, "TEXCOORD_0": 467}, "indices": 468, "material": 25, "mode": 4}]}, {"name": "door_lr_aluminium2.0_0", "primitives": [{"attributes": {"NORMAL": 470, "POSITION": 469, "TEXCOORD_0": 471}, "indices": 472, "material": 30, "mode": 4}]}, {"name": "door_lr_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 474, "POSITION": 473, "TEXCOORD_0": 475}, "indices": 476, "material": 3, "mode": 4}]}, {"name": "door_lr_glass.0_0", "primitives": [{"attributes": {"NORMAL": 478, "POSITION": 477, "TEXCOORD_0": 479}, "indices": 480, "material": 38, "mode": 4}]}, {"name": "door_lr_primary_0", "primitives": [{"attributes": {"NORMAL": 482, "POSITION": 481, "TEXCOORD_0": 483}, "indices": 484, "material": 8, "mode": 4}]}, {"name": "door_lr_primary.002_0", "primitives": [{"attributes": {"NORMAL": 486, "POSITION": 485, "TEXCOORD_0": 487}, "indices": 488, "material": 48, "mode": 4}]}, {"name": "door_lr_ok_primary_0", "primitives": [{"attributes": {"NORMAL": 490, "POSITION": 489, "TEXCOORD_0": 491}, "indices": 492, "material": 8, "mode": 4}]}, {"name": "door_rf_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 494, "POSITION": 493, "TEXCOORD_0": 495}, "indices": 496, "material": 7, "mode": 4}]}, {"name": "door_rf_texture_Buttons.0_0", "primitives": [{"attributes": {"NORMAL": 498, "POSITION": 497, "TEXCOORD_0": 499}, "indices": 500, "material": 34, "mode": 4}]}, {"name": "door_rf_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 502, "POSITION": 501, "TEXCOORD_0": 503}, "indices": 504, "material": 4, "mode": 4}]}, {"name": "door_rf_movsteer_1.0.0_0", "primitives": [{"attributes": {"NORMAL": 506, "POSITION": 505, "TEXCOORD_0": 507}, "indices": 508, "material": 5, "mode": 4}]}, {"name": "door_rf_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 510, "POSITION": 509, "TEXCOORD_0": 511}, "indices": 512, "material": 31, "mode": 4}]}, {"name": "door_rf_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 514, "POSITION": 513, "TEXCOORD_0": 515}, "indices": 516, "material": 25, "mode": 4}]}, {"name": "door_rf_aluminium2.0_0", "primitives": [{"attributes": {"NORMAL": 518, "POSITION": 517, "TEXCOORD_0": 519}, "indices": 520, "material": 30, "mode": 4}]}, {"name": "door_rf_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 522, "POSITION": 521, "TEXCOORD_0": 523}, "indices": 524, "material": 3, "mode": 4}]}, {"name": "door_rf_door_lf.0_0", "primitives": [{"attributes": {"NORMAL": 526, "POSITION": 525, "TEXCOORD_0": 527}, "indices": 528, "material": 46, "mode": 4}]}, {"name": "door_rf_glass.0_0", "primitives": [{"attributes": {"NORMAL": 530, "POSITION": 529, "TEXCOORD_0": 531}, "indices": 532, "material": 38, "mode": 4}]}, {"name": "door_rf_mirror_inside.0_0", "primitives": [{"attributes": {"NORMAL": 534, "POSITION": 533, "TEXCOORD_0": 535}, "indices": 536, "material": 37, "mode": 4}]}, {"name": "door_rf_primary_0", "primitives": [{"attributes": {"NORMAL": 538, "POSITION": 537, "TEXCOORD_0": 539}, "indices": 540, "material": 8, "mode": 4}]}, {"name": "door_rf_primary.002_0", "primitives": [{"attributes": {"NORMAL": 542, "POSITION": 541, "TEXCOORD_0": 543}, "indices": 544, "material": 48, "mode": 4}]}, {"name": "door_rf_ok_primary_0", "primitives": [{"attributes": {"NORMAL": 546, "POSITION": 545, "TEXCOORD_0": 547}, "indices": 548, "material": 8, "mode": 4}]}, {"name": "door_rr_JUST_BLACK.0_0", "primitives": [{"attributes": {"NORMAL": 550, "POSITION": 549, "TEXCOORD_0": 551}, "indices": 552, "material": 7, "mode": 4}]}, {"name": "door_rr_texture_Buttons.0_0", "primitives": [{"attributes": {"NORMAL": 554, "POSITION": 553, "TEXCOORD_0": 555}, "indices": 556, "material": 34, "mode": 4}]}, {"name": "door_rr_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 558, "POSITION": 557, "TEXCOORD_0": 559}, "indices": 560, "material": 4, "mode": 4}]}, {"name": "door_rr_movsteer_1.0.0_0", "primitives": [{"attributes": {"NORMAL": 562, "POSITION": 561, "TEXCOORD_0": 563}, "indices": 564, "material": 5, "mode": 4}]}, {"name": "door_rr_Putih.0_0", "primitives": [{"attributes": {"NORMAL": 566, "POSITION": 565, "TEXCOORD_0": 567}, "indices": 568, "material": 31, "mode": 4}]}, {"name": "door_rr_Plastic.0_0", "primitives": [{"attributes": {"NORMAL": 570, "POSITION": 569, "TEXCOORD_0": 571}, "indices": 572, "material": 25, "mode": 4}]}, {"name": "door_rr_aluminium2.0_0", "primitives": [{"attributes": {"NORMAL": 574, "POSITION": 573, "TEXCOORD_0": 575}, "indices": 576, "material": 30, "mode": 4}]}, {"name": "door_rr_dvorright.0_0", "primitives": [{"attributes": {"NORMAL": 578, "POSITION": 577, "TEXCOORD_0": 579}, "indices": 580, "material": 3, "mode": 4}]}, {"name": "door_rr_glass.0_0", "primitives": [{"attributes": {"NORMAL": 582, "POSITION": 581, "TEXCOORD_0": 583}, "indices": 584, "material": 38, "mode": 4}]}, {"name": "door_rr_primary_0", "primitives": [{"attributes": {"NORMAL": 586, "POSITION": 585, "TEXCOORD_0": 587}, "indices": 588, "material": 8, "mode": 4}]}, {"name": "door_rr_primary.002_0", "primitives": [{"attributes": {"NORMAL": 590, "POSITION": 589, "TEXCOORD_0": 591}, "indices": 592, "material": 48, "mode": 4}]}, {"name": "door_rr_ok_primary.002_0", "primitives": [{"attributes": {"NORMAL": 594, "POSITION": 593, "TEXCOORD_0": 595}, "indices": 596, "material": 48, "mode": 4}]}, {"name": "windscreen_ok_glass.0_0", "primitives": [{"attributes": {"NORMAL": 598, "POSITION": 597, "TEXCOORD_0": 599}, "indices": 600, "material": 38, "mode": 4}]}, {"name": "front_black_front_black.0_0", "primitives": [{"attributes": {"NORMAL": 602, "POSITION": 601, "TEXCOORD_0": 603}, "indices": 604, "material": 50, "mode": 4}]}, {"name": "chrome.002_back_chrome_light.0_0", "primitives": [{"attributes": {"NORMAL": 606, "POSITION": 605, "TEXCOORD_0": 607}, "indices": 608, "material": 11, "mode": 4}]}, {"name": "foglights_r_foglight r_0", "primitives": [{"attributes": {"NORMAL": 610, "POSITION": 609, "TEXCOORD_0": 611}, "indices": 612, "material": 15, "mode": 4}]}, {"name": "foglights_l_foglight l_0", "primitives": [{"attributes": {"NORMAL": 614, "POSITION": 613, "TEXCOORD_0": 615}, "indices": 616, "material": 16, "mode": 4}]}, {"name": "indicator_lights_r_indicator rf_0", "primitives": [{"attributes": {"NORMAL": 618, "POSITION": 617, "TEXCOORD_0": 619, "TEXCOORD_1": 620}, "indices": 621, "material": 23, "mode": 4}]}, {"name": "indicator_lights_l_indicator lf_0", "primitives": [{"attributes": {"NORMAL": 623, "POSITION": 622, "TEXCOORD_0": 624, "TEXCOORD_1": 625}, "indices": 626, "material": 22, "mode": 4}]}, {"name": "tembus_depan_ok_tembus red.0_0", "primitives": [{"attributes": {"NORMAL": 628, "POSITION": 627, "TEXCOORD_0": 629}, "indices": 630, "material": 20, "mode": 4}]}, {"name": "front_bumper_ok_primary_0", "primitives": [{"attributes": {"NORMAL": 632, "POSITION": 631, "TEXCOORD_0": 633}, "indices": 634, "material": 8, "mode": 4}]}, {"name": "rear_bumper_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 636, "POSITION": 635, "TEXCOORD_0": 637}, "indices": 638, "material": 4, "mode": 4}]}, {"name": "rear_bumper_ok_primary_0", "primitives": [{"attributes": {"NORMAL": 640, "POSITION": 639, "TEXCOORD_0": 641}, "indices": 642, "material": 8, "mode": 4}]}, {"name": "light_pantulan_light_pantulan.0_0", "primitives": [{"attributes": {"NORMAL": 644, "POSITION": 643, "TEXCOORD_0": 645, "TEXCOORD_1": 646}, "indices": 647, "material": 51, "mode": 4}]}, {"name": "tembus_belakang_tembus red.0_0", "primitives": [{"attributes": {"NORMAL": 649, "POSITION": 648, "TEXCOORD_0": 650}, "indices": 651, "material": 20, "mode": 4}]}, {"name": "bonnet_ok_primary_0", "primitives": [{"attributes": {"NORMAL": 653, "POSITION": 652, "TEXCOORD_0": 654}, "indices": 655, "material": 8, "mode": 4}]}, {"name": "chrome_bonnet_ok_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 657, "POSITION": 656, "TEXCOORD_0": 658}, "indices": 659, "material": 4, "mode": 4}]}, {"name": "wheels_wheels.2_0", "primitives": [{"attributes": {"NORMAL": 661, "POSITION": 660, "TEXCOORD_0": 662}, "indices": 663, "material": 52, "mode": 4}]}, {"name": "wheels_wheels.0_0", "primitives": [{"attributes": {"NORMAL": 665, "POSITION": 664, "TEXCOORD_0": 666}, "indices": 667, "material": 53, "mode": 4}]}, {"name": "wheels_wheels.1_0", "primitives": [{"attributes": {"NORMAL": 669, "POSITION": 668, "TEXCOORD_0": 670}, "indices": 671, "material": 54, "mode": 4}]}, {"name": "wheels_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 673, "POSITION": 672, "TEXCOORD_0": 674}, "indices": 675, "material": 4, "mode": 4}]}, {"name": "wheels_wheels.3_0", "primitives": [{"attributes": {"NORMAL": 677, "POSITION": 676, "TEXCOORD_0": 678}, "indices": 679, "material": 55, "mode": 4}]}, {"name": "wheels_wheels.4_0", "primitives": [{"attributes": {"NORMAL": 681, "POSITION": 680, "TEXCOORD_0": 682}, "indices": 683, "material": 56, "mode": 4}]}, {"name": "wheels_wheels.6_0", "primitives": [{"attributes": {"NORMAL": 685, "POSITION": 684, "TEXCOORD_0": 686}, "indices": 687, "material": 57, "mode": 4}]}, {"name": "wheels.001_wheels.2_0", "primitives": [{"attributes": {"NORMAL": 689, "POSITION": 688, "TEXCOORD_0": 690}, "indices": 691, "material": 52, "mode": 4}]}, {"name": "wheels.001_wheels.0_0", "primitives": [{"attributes": {"NORMAL": 693, "POSITION": 692, "TEXCOORD_0": 694}, "indices": 695, "material": 53, "mode": 4}]}, {"name": "wheels.001_wheels.1_0", "primitives": [{"attributes": {"NORMAL": 697, "POSITION": 696, "TEXCOORD_0": 698}, "indices": 699, "material": 54, "mode": 4}]}, {"name": "wheels.001_movsteer_1.0.1_0", "primitives": [{"attributes": {"NORMAL": 701, "POSITION": 700, "TEXCOORD_0": 702}, "indices": 703, "material": 4, "mode": 4}]}, {"name": "wheels.001_wheels.3_0", "primitives": [{"attributes": {"NORMAL": 705, "POSITION": 704, "TEXCOORD_0": 706}, "indices": 707, "material": 55, "mode": 4}]}, {"name": "wheels.001_wheels.4_0", "primitives": [{"attributes": {"NORMAL": 709, "POSITION": 708, "TEXCOORD_0": 710}, "indices": 711, "material": 56, "mode": 4}]}, {"name": "wheels.001_wheels.6_0", "primitives": [{"attributes": {"NORMAL": 713, "POSITION": 712, "TEXCOORD_0": 714}, "indices": 715, "material": 57, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "888d5b4c0a7b42299d217c2edec42266.fbx"}, {"children": [3], "name": "RootNode"}, {"children": [4, 6, 8, 11, 14, 20, 25, 284], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Tesla Model 3"}, {"children": [5], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.8983383774757385, -1.4465434551239014, -0.37177029252052307, 1.0], "name": "hub_rb"}, {"mesh": 0, "name": "hub_rb_hub_rb.0_0"}, {"children": [7], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, -0.8982263803482056, -1.4464672803878784, -0.37177029252052307, 1.0], "name": "hub_lb"}, {"mesh": 1, "name": "hub_lb_hub_rb.0_0"}, {"children": [9, 10], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.924155056476593, 1.7566020488739014, -0.37179121375083923, 1.0], "name": "hub_rf"}, {"mesh": 2, "name": "hub_rf_hub_rf.0_0"}, {"mesh": 3, "name": "hub_rf_hub_rf.1_0"}, {"children": [12, 13], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, -0.9248740077018738, 1.7566020488739014, -0.37179121375083923, 1.0], "name": "hub_lf"}, {"mesh": 4, "name": "hub_lf_hub_rf.0_0"}, {"mesh": 5, "name": "hub_lf_hub_rf.1_0"}, {"children": [15, 17], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.5770755119004907, 0.8166903564443364, 0.0, 0.0, -0.8166905024800044, 0.5770756150896703, 0.0, -0.08287635445594788, 1.604562520980835, 0.1773100644350052, 1.0], "name": "dvornik_dummy"}, {"children": [16], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -1.7208456881689922e-15, 0.0, 0.0, 1.7208454830282003e-15, 0.9999998807907104, 0.0, -7.079541683197021e-05, 4.8041343688964844e-05, -6.198883056640625e-05, 1.0], "name": "dvor<PERSON>"}, {"mesh": 6, "name": "dvorright_dvorright.0_0"}, {"children": [18], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.9912313423487227, 0.13213745394952725, 0.0, 0.0, -0.13213746182553374, 0.9912314014307182, 0.0, -0.6228156685829163, -0.07923680543899536, 0.1706840991973877, 1.0], "name": "other"}, {"children": [19], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0000001192092896, -1.110223024625157e-16, 0.0, 0.0, 1.110223024625157e-16, 1.0000001192092896, 0.0, -7.086992263793945e-05, 3.916025161743164e-05, -6.783008575439453e-05, 1.0], "name": "dvorleft"}, {"mesh": 7, "name": "dvorleft_dvorright.0_0"}, {"children": [21], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.9530583634485849, -0.3027856664611888, 0.0, 0.0, 0.30278575669837615, 0.9530586474821955, 0.0, -0.4699576199054718, 0.6079102754592896, 0.17580989003181458, 1.0], "name": "steering_dummy"}, {"children": [22, 23, 24], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.9999999403953548, -2.980232072236077e-08, 0.0, 0.0, 2.9802322498717615e-08, 0.9999999999999996, 0.0, 7.420778274536133e-06, 5.0067901611328125e-06, 6.854534149169922e-07, 1.0], "name": "movsteer_1.0"}, {"mesh": 8, "name": "movsteer_1.0_movsteer_1.0.1_0"}, {"mesh": 9, "name": "movsteer_1.0_movsteer_1.0.0_0"}, {"mesh": 10, "name": "movsteer_1.0_dvorright.0_0"}, {"children": [26, 158, 184, 202, 218, 235, 250, 253, 270, 279], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "chassis_dummy"}, {"children": [27, 28, 32, 34, 36, 71, 155], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, 1.7706135511398315, -0.36240634322166443, 1.0], "name": "chassis"}, {"mesh": 11, "name": "chassis_chassis.0_0"}, {"children": [29, 30], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -1.7706135511398315, 0.36240634322166443, 1.0], "name": "JUST_BLACK"}, {"mesh": 12, "name": "JUST_BLACK_JUST_BLACK.0_0"}, {"children": [31], "name": "JUST_BLACK.001"}, {"mesh": 13, "name": "JUST_BLACK.001_JUST_BLACK.0_0"}, {"children": [33], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -1.7706135511398315, 0.36240634322166443, 1.0], "name": "body"}, {"mesh": 14, "name": "body_primary_0"}, {"children": [35], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -1.7706135511398315, 0.36240634322166443, 1.0], "name": "bodysills"}, {"mesh": 15, "name": "bodysills_primary.001_0"}, {"children": [37, 38, 42, 50, 52, 54, 57, 59, 61, 63, 65, 67, 69], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -1.7706135511398315, 0.36240634322166443, 1.0], "name": "black_lights"}, {"mesh": 16, "name": "black_lights_black_lights.0_0"}, {"children": [39, 40], "name": "_satin_black_134"}, {"mesh": 17, "name": "_satin_black_134_black_lights.0_0"}, {"children": [41], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -5.293955920339376e-23, 0.0, 0.0, 5.293955920339376e-23, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "_plastic_black_124"}, {"mesh": 18, "name": "_plastic_black_124_black_lights.0_0"}, {"children": [43, 44, 46, 48], "name": "back_chrome_light"}, {"mesh": 19, "name": "back_chrome_light_back_chrome_light.0_0"}, {"children": [45], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -5.293955920339376e-23, 0.0, 0.0, 5.293955920339376e-23, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "pantulans"}, {"mesh": 20, "name": "pantulans_pantulans.0_0"}, {"children": [47], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -5.293955920339376e-23, 0.0, 0.0, 5.293955920339376e-23, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "rear_lights"}, {"mesh": 21, "name": "rear_lights_right rear light_0"}, {"children": [49], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -5.293955920339376e-23, 0.0, 0.0, 5.293955920339376e-23, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "light_breake"}, {"mesh": 22, "name": "light_breake_breaklight l_0"}, {"children": [51], "name": "chrome_foglight_r"}, {"mesh": 23, "name": "chrome_foglight_r_foglight r_0"}, {"children": [53], "name": "chrome_foglight_l"}, {"mesh": 24, "name": "chrome_foglight_l_foglight l_0"}, {"children": [55, 56], "name": "chrome_Lights_head_l"}, {"mesh": 25, "name": "chrome_Lights_head_l_right front light_0"}, {"mesh": 26, "name": "chrome_Lights_head_l_left front light_0"}, {"children": [58], "name": "chrome"}, {"mesh": 27, "name": "chrome_movsteer_1.0.1_0"}, {"children": [60], "name": "breake_int"}, {"mesh": 28, "name": "breake_int_breaklight l_0"}, {"children": [62], "name": "aluminium_light"}, {"mesh": 29, "name": "aluminium_light_aluminium_light.0_0"}, {"children": [64], "name": "tembus red"}, {"mesh": 30, "name": "tembus red_tembus red.0_0"}, {"children": [66], "name": "interiorlights"}, {"mesh": 31, "name": "interiorlights_light night_0"}, {"children": [68], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0018170475959777832, 0.0, 0.0, 1.0], "name": "turn_indicat_l"}, {"mesh": 32, "name": "turn_indicat_l_indicator lf_0"}, {"children": [70], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0018170475959777832, 0.0, 0.0, 1.0], "name": "turn_indicat_r"}, {"mesh": 33, "name": "turn_indicat_r_indicator rf_0"}, {"children": [72, 73, 75, 77, 79, 81, 83, 85, 87, 89, 91, 94, 96, 98, 100, 102, 104, 106, 108, 110, 112, 114, 116, 118, 120, 122, 124, 126, 128, 130, 132, 134, 136, 138, 140, 142, 145, 147, 149, 153], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -1.7706135511398315, 0.36240634322166443, 1.0], "name": "base"}, {"mesh": 34, "name": "base_dvorright.0_0"}, {"children": [74], "name": "hitam"}, {"mesh": 35, "name": "hitam_dvorright.0_0"}, {"children": [76], "name": "hitam.001"}, {"mesh": 36, "name": "hitam.001_dvorright.0_0"}, {"children": [78], "name": "hitam.002"}, {"mesh": 37, "name": "hitam.002_hitam.0_0"}, {"children": [80], "name": "Plastic"}, {"mesh": 38, "name": "Plastic_Plastic.0_0"}, {"children": [82], "name": "belt"}, {"mesh": 39, "name": "belt_belt.0_0"}, {"children": [84], "name": "black"}, {"mesh": 40, "name": "black_black_lights.0_0"}, {"children": [86], "name": "satin_red"}, {"mesh": 41, "name": "satin_red_satin_red.0_0"}, {"children": [88], "name": "paint_black"}, {"mesh": 42, "name": "paint_black_dvorright.0_0"}, {"children": [90], "name": "cahrome"}, {"mesh": 43, "name": "cahrome_movsteer_1.0.1_0"}, {"children": [92, 93], "name": "suspensi"}, {"mesh": 44, "name": "suspensi_suspensi.0_0"}, {"mesh": 45, "name": "suspensi_suspensi.1_0"}, {"children": [95], "name": "chrome_"}, {"mesh": 46, "name": "chrome__movsteer_1.0.1_0"}, {"children": [97], "name": "chrome1"}, {"mesh": 47, "name": "chrome1_movsteer_1.0.1_0"}, {"children": [99], "name": "chrome2"}, {"mesh": 48, "name": "chrome2_movsteer_1.0.1_0"}, {"children": [101], "name": "hitam.003"}, {"mesh": 49, "name": "hitam.003_dvorright.0_0"}, {"children": [103], "name": "hitam.004"}, {"mesh": 50, "name": "hitam.004_dvorright.0_0"}, {"children": [105], "name": "aluminium"}, {"mesh": 51, "name": "aluminium_movsteer_1.0.1_0"}, {"children": [107], "name": "hitam.005"}, {"mesh": 52, "name": "hitam.005_Plastic.0_0"}, {"children": [109], "name": "hitam.006"}, {"mesh": 53, "name": "hitam.006_black_lights.0_0"}, {"children": [111], "name": "texture_Leather"}, {"mesh": 54, "name": "texture_Leather_movsteer_1.0.0_0"}, {"children": [113], "name": "texture_Leather.001"}, {"mesh": 55, "name": "texture_Leather.001_movsteer_1.0.0_0"}, {"children": [115], "name": "aluminium2"}, {"mesh": 56, "name": "aluminium2_aluminium2.0_0"}, {"children": [117], "name": "frunkplastic"}, {"mesh": 57, "name": "frunkplastic_dvorright.0_0"}, {"children": [119], "name": "<PERSON><PERSON>"}, {"mesh": 58, "name": "Putih_Putih.0_0"}, {"children": [121], "name": "whiteleather"}, {"mesh": 59, "name": "whiteleather_Putih.0_0"}, {"children": [123], "name": "Putih.001"}, {"mesh": 60, "name": "Putih.001_Putih.0_0"}, {"children": [125], "name": "Putih.002"}, {"mesh": 61, "name": "Putih.002_Putih.0_0"}, {"children": [127], "name": "Carpet"}, {"mesh": 62, "name": "Carpet_Carpet.0_0"}, {"children": [129], "name": "black.001"}, {"mesh": 63, "name": "black.001_Plastic.0_0"}, {"children": [131], "name": "black.002"}, {"mesh": 64, "name": "black.002_Plastic.0_0"}, {"children": [133], "name": "black.003"}, {"mesh": 65, "name": "black.003_black_lights.0_0"}, {"children": [135], "name": "black.004"}, {"mesh": 66, "name": "black.004_JUST_BLACK.0_0"}, {"children": [137], "name": "black.005"}, {"mesh": 67, "name": "black.005_black_lights.0_0"}, {"children": [139], "name": "Carpet_Light"}, {"mesh": 68, "name": "Carpet_Light_Carpet_Light.0_0"}, {"children": [141], "name": "chromeBELT"}, {"mesh": 69, "name": "chromeBELT_movsteer_1.0.1_0"}, {"children": [143, 144], "name": "suspensi2"}, {"mesh": 70, "name": "suspensi2_suspensi.0_0"}, {"mesh": 71, "name": "suspensi2_suspensi.1_0"}, {"children": [146], "name": "texture_Buttons"}, {"mesh": 72, "name": "texture_Buttons_texture_Buttons.0_0"}, {"children": [148], "name": "LCDs"}, {"mesh": 73, "name": "LCDs_LCDs.0_0"}, {"children": [150, 151], "name": "<PERSON><PERSON> Leather white"}, {"mesh": 74, "name": "<PERSON><PERSON> Leather white_<PERSON><PERSON> Leather white.0_0"}, {"children": [152], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -5.293955920339376e-23, 0.0, 0.0, 5.293955920339376e-23, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Leather_white"}, {"mesh": 75, "name": "<PERSON><PERSON>_white_<PERSON><PERSON> Leather white.0_0"}, {"children": [154], "name": "mirror_inside"}, {"mesh": 76, "name": "mirror_inside_mirror_inside.0_0"}, {"children": [156, 157], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -1.0415847301483154, 0.9349302053451538, 1.0], "name": "glass"}, {"mesh": 77, "name": "glass_glass.0_0"}, {"mesh": 78, "name": "glass_glass.1_0"}, {"children": [159], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -1.692225694656372, 0.5972447991371155, 1.0], "name": "boot_dummy"}, {"children": [160, 161, 163, 168, 170, 172, 174, 176, 178, 180, 182], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, 8.463859558105469e-06, 1.5139579772949219e-05, 1.0], "name": "black_boot"}, {"mesh": 79, "name": "black_boot_black_lights.0_0"}, {"children": [162], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -2.1576881408691406e-05, -1.0967254638671875e-05, 1.0], "name": "boot"}, {"mesh": 80, "name": "boot_primary_0"}, {"children": [164, 165, 166, 167], "name": "platnomor"}, {"mesh": 81, "name": "platnomor_JUST_BLACK.0_0"}, {"mesh": 82, "name": "platnomor_platnomor.1_0"}, {"mesh": 83, "name": "platnomor_hitam.0_0"}, {"mesh": 84, "name": "platnomor_platnomor.2_0"}, {"children": [169], "name": "chrome.001"}, {"mesh": 85, "name": "chrome.001_movsteer_1.0.1_0"}, {"children": [171], "name": "light_turn_rr_boot"}, {"mesh": 86, "name": "light_turn_rr_boot_indicator rr_0"}, {"children": [173], "name": "light_turn_lr_boot"}, {"mesh": 87, "name": "light_turn_lr_boot_indicator lr_0"}, {"children": [175], "name": "chrome_light"}, {"mesh": 88, "name": "chrome_light_back_chrome_light.0_0"}, {"children": [177], "name": "rear_lightsr"}, {"mesh": 89, "name": "rear_lightsr_right rear light_0"}, {"children": [179], "name": "rear_lightsl"}, {"mesh": 90, "name": "rear_lightsl_left rear light_0"}, {"children": [181], "name": "lightrevese_boot"}, {"mesh": 91, "name": "lightrevese_boot_revlight L_0"}, {"children": [183], "name": "tembus_boot_ok"}, {"mesh": 92, "name": "tembus_boot_ok_tembus red.0_0"}, {"children": [185], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, -1.005982756614685, 1.1752837896347046, -0.06258115917444229, 1.0], "name": "door_lf_dummy"}, {"children": [186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 7.3909759521484375e-06, -4.172325134277344e-06, 6.936490535736084e-06, 1.0], "name": "door_lf"}, {"mesh": 93, "name": "door_lf_JUST_BLACK.0_0"}, {"mesh": 94, "name": "door_lf_texture_Buttons.0_0"}, {"mesh": 95, "name": "door_lf_movsteer_1.0.1_0"}, {"mesh": 96, "name": "door_lf_movsteer_1.0.0_0"}, {"mesh": 97, "name": "door_lf_Putih.0_0"}, {"mesh": 98, "name": "door_lf_Plastic.0_0"}, {"mesh": 99, "name": "door_lf_aluminium2.0_0"}, {"mesh": 100, "name": "door_lf_dvorright.0_0"}, {"mesh": 101, "name": "door_lf_door_lf.0_0"}, {"mesh": 102, "name": "door_lf_door_lf.5_0"}, {"mesh": 103, "name": "door_lf_glass.0_0"}, {"mesh": 104, "name": "door_lf_mirror_inside.0_0"}, {"mesh": 105, "name": "door_lf_primary_0"}, {"mesh": 106, "name": "door_lf_primary.002_0"}, {"children": [201], "name": "door_lf_ok"}, {"mesh": 107, "name": "door_lf_ok_primary_0"}, {"children": [203], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, -1.0332930088043213, -0.13056010007858276, -0.06258325278759003, 1.0], "name": "door_lr_dummy"}, {"children": [204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 5.841255187988281e-06, 7.599592208862305e-07, -5.587935447692871e-07, 1.0], "name": "door_lr"}, {"mesh": 108, "name": "door_lr_JUST_BLACK.0_0"}, {"mesh": 109, "name": "door_lr_texture_Buttons.0_0"}, {"mesh": 110, "name": "door_lr_primary.004_0"}, {"mesh": 111, "name": "door_lr_movsteer_1.0.1_0"}, {"mesh": 112, "name": "door_lr_movsteer_1.0.0_0"}, {"mesh": 113, "name": "door_lr_Putih.0_0"}, {"mesh": 114, "name": "door_lr_Plastic.0_0"}, {"mesh": 115, "name": "door_lr_aluminium2.0_0"}, {"mesh": 116, "name": "door_lr_dvorright.0_0"}, {"mesh": 117, "name": "door_lr_glass.0_0"}, {"mesh": 118, "name": "door_lr_primary_0"}, {"mesh": 119, "name": "door_lr_primary.002_0"}, {"children": [217], "name": "door_lr_ok"}, {"mesh": 120, "name": "door_lr_ok_primary_0"}, {"children": [219], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 1.0057878494262695, 1.1752837896347046, -0.06258115917444229, 1.0], "name": "door_rf_dummy"}, {"children": [220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, -2.384185791015625e-07, -4.172325134277344e-06, 6.936490535736084e-06, 1.0], "name": "door_rf"}, {"mesh": 121, "name": "door_rf_JUST_BLACK.0_0"}, {"mesh": 122, "name": "door_rf_texture_Buttons.0_0"}, {"mesh": 123, "name": "door_rf_movsteer_1.0.1_0"}, {"mesh": 124, "name": "door_rf_movsteer_1.0.0_0"}, {"mesh": 125, "name": "door_rf_Putih.0_0"}, {"mesh": 126, "name": "door_rf_Plastic.0_0"}, {"mesh": 127, "name": "door_rf_aluminium2.0_0"}, {"mesh": 128, "name": "door_rf_dvorright.0_0"}, {"mesh": 129, "name": "door_rf_door_lf.0_0"}, {"mesh": 130, "name": "door_rf_glass.0_0"}, {"mesh": 131, "name": "door_rf_mirror_inside.0_0"}, {"mesh": 132, "name": "door_rf_primary_0"}, {"mesh": 133, "name": "door_rf_primary.002_0"}, {"children": [234], "name": "door_rf_ok"}, {"mesh": 134, "name": "door_rf_ok_primary_0"}, {"children": [236], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 1.033092737197876, -0.13055986166000366, -0.06258325278759003, 1.0], "name": "door_rr_dummy"}, {"children": [237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 5.960464477539062e-07, 5.21540641784668e-07, -5.587935447692871e-07, 1.0], "name": "door_rr"}, {"mesh": 135, "name": "door_rr_JUST_BLACK.0_0"}, {"mesh": 136, "name": "door_rr_texture_Buttons.0_0"}, {"mesh": 137, "name": "door_rr_movsteer_1.0.1_0"}, {"mesh": 138, "name": "door_rr_movsteer_1.0.0_0"}, {"mesh": 139, "name": "door_rr_Putih.0_0"}, {"mesh": 140, "name": "door_rr_Plastic.0_0"}, {"mesh": 141, "name": "door_rr_aluminium2.0_0"}, {"mesh": 142, "name": "door_rr_dvorright.0_0"}, {"mesh": 143, "name": "door_rr_glass.0_0"}, {"mesh": 144, "name": "door_rr_primary_0"}, {"mesh": 145, "name": "door_rr_primary.002_0"}, {"children": [249], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.00027483701705932617, 1.0], "name": "door_rr_ok"}, {"mesh": 146, "name": "door_rr_ok_primary.002_0"}, {"children": [251], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, 0.7290275692939758, 0.5725256204605103, 1.0], "name": "windscreen_dummy"}, {"children": [252], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, 1.2516975402832031e-06, -1.7881393432617188e-06, 1.0], "name": "windscreen_ok"}, {"mesh": 147, "name": "windscreen_ok_glass.0_0"}, {"children": [254, 268], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.8733397722244263, 1.9314100742340088, 0.12849067151546478, 1.0], "name": "bump_front_dummy"}, {"children": [255, 256, 258, 260, 262, 264, 266], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 4.738569259643555e-05, -1.1920928955078125e-07, -1.9371509552001953e-07, 1.0], "name": "front_black"}, {"mesh": 148, "name": "front_black_front_black.0_0"}, {"children": [257], "name": "chrome.002"}, {"mesh": 149, "name": "chrome.002_back_chrome_light.0_0"}, {"children": [259], "name": "foglights_r"}, {"mesh": 150, "name": "foglights_r_foglight r_0"}, {"children": [261], "name": "foglights_l"}, {"mesh": 151, "name": "foglights_l_foglight l_0"}, {"children": [263], "name": "indicator_lights_r"}, {"mesh": 152, "name": "indicator_lights_r_indicator rf_0"}, {"children": [265], "name": "indicator_lights_l"}, {"mesh": 153, "name": "indicator_lights_l_indicator lf_0"}, {"children": [267], "name": "tembus_depan_ok"}, {"mesh": 154, "name": "tembus_depan_ok_tembus red.0_0"}, {"children": [269], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 2.294778823852539e-05, 1.0848045349121094e-05, 2.7224421501159668e-05, 1.0], "name": "front_bumper_ok"}, {"mesh": 155, "name": "front_bumper_ok_primary_0"}, {"children": [271], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.8733397722244263, -1.9287632703781128, 0.28136900067329407, 1.0], "name": "bump_rear_dummy"}, {"children": [272, 273, 275, 277], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, -2.1457672119140625e-06, -5.960464477539062e-07, 1.519918441772461e-05, 1.0], "name": "rear_bumper"}, {"mesh": 156, "name": "rear_bumper_movsteer_1.0.1_0"}, {"children": [274], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 2.771615982055664e-05, 1.3828277587890625e-05, -1.5079975128173828e-05, 1.0], "name": "rear_bumper_ok"}, {"mesh": 157, "name": "rear_bumper_ok_primary_0"}, {"children": [276], "name": "light_pantulan"}, {"mesh": 158, "name": "light_pantulan_light_pantulan.0_0"}, {"children": [278], "name": "tembus_belakang"}, {"mesh": 159, "name": "tembus_belakang_tembus red.0_0"}, {"children": [280], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, 1.3263945579528809, 0.2373909056186676, 1.0], "name": "bonnet_dummy"}, {"children": [281, 282], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.0, -9.5367431640625e-07, 1.0102987289428711e-05, 1.0], "name": "bonnet_ok"}, {"mesh": 160, "name": "bonnet_ok_primary_0"}, {"children": [283], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -4.76837158203125e-07, -2.384185791015625e-07, 1.0], "name": "chrome_bonnet_ok"}, {"mesh": 161, "name": "chrome_bonnet_ok_movsteer_1.0.1_0"}, {"children": [285, 293], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, 0.9250742793083191, 1.7566150426864624, -0.37177035212516785, 1.0], "name": "wheel_rf_dummy"}, {"children": [286, 287, 288, 289, 290, 291, 292], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -2.646977960169688e-23, 0.0, 0.0, 2.646977960169688e-23, 1.0, 0.0, -7.224082946777344e-05, 3.612041473388672e-05, 2.491474151611328e-05, 1.0], "name": "wheels"}, {"mesh": 162, "name": "wheels_wheels.2_0"}, {"mesh": 163, "name": "wheels_wheels.0_0"}, {"mesh": 164, "name": "wheels_wheels.1_0"}, {"mesh": 165, "name": "wheels_movsteer_1.0.1_0"}, {"mesh": 166, "name": "wheels_wheels.3_0"}, {"mesh": 167, "name": "wheels_wheels.4_0"}, {"mesh": 168, "name": "wheels_wheels.6_0"}, {"children": [294, 295, 296, 297, 298, 299, 300], "matrix": [1.018071174621582, 0.0, 0.0, 0.0, 0.0, 1.018071174621582, -2.6469779601696874e-23, 0.0, 0.0, 2.6469779601696874e-23, 1.018071174621582, 0.0, -7.224082946777344e-05, -3.2039804458618164, 2.491474151611328e-05, 1.0], "name": "wheels.001"}, {"mesh": 169, "name": "wheels.001_wheels.2_0"}, {"mesh": 170, "name": "wheels.001_wheels.0_0"}, {"mesh": 171, "name": "wheels.001_wheels.1_0"}, {"mesh": 172, "name": "wheels.001_movsteer_1.0.1_0"}, {"mesh": 173, "name": "wheels.001_wheels.3_0"}, {"mesh": 174, "name": "wheels.001_wheels.4_0"}, {"mesh": 175, "name": "wheels.001_wheels.6_0"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}, {"sampler": 0, "source": 2}, {"sampler": 0, "source": 3}, {"sampler": 0, "source": 4}, {"sampler": 0, "source": 5}, {"sampler": 0, "source": 6}, {"sampler": 0, "source": 7}, {"sampler": 0, "source": 8}, {"sampler": 0, "source": 9}, {"sampler": 0, "source": 10}, {"sampler": 0, "source": 11}, {"sampler": 0, "source": 12}, {"sampler": 0, "source": 13}, {"sampler": 0, "source": 14}, {"sampler": 0, "source": 15}, {"sampler": 0, "source": 16}, {"sampler": 0, "source": 17}, {"sampler": 0, "source": 18}, {"sampler": 0, "source": 19}, {"sampler": 0, "source": 20}, {"sampler": 0, "source": 21}]}