import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/Addons.js';
const width = 800
const height = 600
//创建场景
const scene = new THREE.Scene();

//辅助线
const axesHelper = new THREE.AxesHelper(5);
scene.add(axesHelper);

//创建光源
const light = new THREE.DirectionalLight(0xffffff, 1);
light.position.set(1, 1, 1);
scene.add(light);

//创建环境光
const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
scene.add(ambientLight);
//
let mixer: THREE.AnimationMixer;
//加载模型
const loader = new GLTFLoader();
loader.load('./aaa.glb', (gltf) => {
    console.log(gltf.animations)
    mixer = new THREE.AnimationMixer(gltf.scene); //初始化动画混合器
    const action = mixer.clipAction(gltf.animations[0]) //获取动画
    action.play() //播放动画
    scene.add(gltf.scene);
});

//创建相机
const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
camera.position.set(0, 0, 10);
camera.lookAt(0, 0, 0);
scene.add(camera);

//渲染
const renderer = new THREE.WebGLRenderer({
    canvas: document.querySelector('#canvas') as HTMLCanvasElement,
    antialias: true,
});

const controls = new OrbitControls(camera, renderer.domElement);
renderer.setSize(width, height);
renderer.render(scene, camera);
renderer.setClearColor(0x87CEEB,1) //设置背景色
const clock = new THREE.Clock()//他会根据你系统是60帧还是30帧来计算
const animate = () => {
    requestAnimationFrame(animate);
    renderer.render(scene, camera);
    controls.update();
    if(mixer){
        mixer.update(clock.getDelta())
    }
}
animate();





