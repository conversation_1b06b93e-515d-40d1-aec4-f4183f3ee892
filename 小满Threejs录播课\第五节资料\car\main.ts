import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'; // 控制器 方便调试
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js'; // 加载模型
import genColor from './color';
import {decoration} from './decoration';
//创建场景
const scene = new THREE.Scene();


//添加辅助线
const axesHelper = new THREE.AxesHelper(5);
scene.add(axesHelper);

//创建环境光
const ambientLight = new THREE.AmbientLight(0xffffff, 1);
scene.add(ambientLight);




//scene.background = new THREE.Color(0xFFFFBB);

//引入模型
const loader = new GLTFLoader();
loader.load('./assets/scene.gltf', function(gltf) {
    scene.add(gltf.scene); // 添加到场景
    genColor(scene);
    decoration(scene);
});


//创建摄像机
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
camera.position.set(0, 0, 500);
camera.lookAt(0, 0, 0);


//创建渲染器
const renderer = new THREE.WebGLRenderer({
    canvas: document.querySelector('#canvas') as HTMLCanvasElement,
    antialias: true,
});
renderer.setPixelRatio(window.devicePixelRatio);
renderer.setSize(window.innerWidth, window.innerHeight);

renderer.render(scene, camera);

const controls = new OrbitControls(camera, renderer.domElement);

controls.autoRotate = true;
controls.autoRotateSpeed = 1;

const animate = () => {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
}
animate();




