import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { GLTFLoader } from 'three/examples/jsm/Addons.js';
//绘制线条 三角形
//自定义几何体 BufferGeometry
const width = 800
const height = 600
//创建场景
const scene = new THREE.Scene();

const ambientLight = new THREE.AmbientLight(0xffffff, 1);
scene.add(ambientLight);

const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set(100, 100, 100);
scene.add(directionalLight);

//辅助坐标轴
const axesHelper = new THREE.AxesHelper(100);
scene.add(axesHelper);

const loader = new GLTFLoader();
loader.load('./assets/jug/jug_01_2k.gltf', (gltf) => {
    gltf.scene.scale.set(100, 100, 100);
    scene.add(gltf.scene);
});


//map关联贴图
//创建一个相机
const camera = new THREE.PerspectiveCamera(60, width / height);
camera.position.set(200, 200, 100);
//camera.lookAt(mesh.position);
//将相机添加到场景中
scene.add(camera);



//创建一个渲染器
const renderer = new THREE.WebGLRenderer({
    canvas: document.querySelector('#canvas') as HTMLCanvasElement,
    antialias: true,
});
renderer.setSize(width, height);
//renderer.render(scene, camera);
const controls = new OrbitControls(camera, renderer.domElement);
const animate = () => {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
}
animate();





