import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
//绘制线条 三角形
//自定义几何体 BufferGeometry
const width = 800
const height = 600
//创建场景
const scene = new THREE.Scene();

//辅助坐标轴
const axesHelper = new THREE.AxesHelper(100);
scene.add(axesHelper);

//创建一个几何
//---------------------------------------------
//Vector2 Vector3 Vector4
//Vector2 X Y 2D
//Vector3 X Y Z 3D
//Vector4 X Y Z W 4D 齐次坐标系 平移 旋转 缩放 投影
const geometry = new THREE.BufferGeometry();
//把这个属性设置到几何体上 属性名称是position threejs会通过它确定顶点的位置 32位浮点数 3个为一组
// XYZ
geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array([
    80,580,0,
    720,520,0,
    400,80,0,
]), 3));
//3 XYZ

const material = new THREE.LineBasicMaterial({
    color: 0x00ff00,
    linewidth: 100,
    linecap: 'round', //线帽 A-B-C
    linejoin: 'round', //线连接 A-B
});

// const positions:THREE.Vector3[] = []


//贝塞尔曲线
//欧拉角
//笛卡尔积

// positions.push(
//     new THREE.Vector3(0,0,0), //起点 x y z
//     new THREE.Vector3(100,0,0), //终点 x y z
//     new THREE.Vector3(0,100,0),
//     new THREE.Vector3(100,100,0),
// )

//线条专属材质
//const material = new THREE.LineBasicMaterial({color: 0x00ff00,linewidth: 10});
//添加顶点
//geometry.setFromPoints(positions);
//创建线条 创建网格现在是创建线条
//line LineLoop区别
//line 两端
//LineLoop 闭合 A -B -C 自动闭合
const line = new THREE.LineLoop(geometry, material);
//添加到场景中
scene.add(line);
//---------------------------------------------

//创建一个材质
//const material = new THREE.MeshBasicMaterial({color: 0x00ff00});

//创建一个网格
//const mesh = new THREE.Mesh(geometry, material);

//将网格添加到场景中
//scene.add(mesh);

//创建一个相机
const camera = new THREE.PerspectiveCamera(60, width / height);
camera.position.set(200, 200, 100);
camera.lookAt(line.position);
//将相机添加到场景中
scene.add(camera);



//创建一个渲染器
const renderer = new THREE.WebGLRenderer({
    canvas: document.querySelector('#canvas') as HTMLCanvasElement,
    antialias: true,
});
renderer.setSize(width, height);
//renderer.render(scene, camera);
const controls = new OrbitControls(camera, renderer.domElement);
const animate = () => {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
}
animate();





