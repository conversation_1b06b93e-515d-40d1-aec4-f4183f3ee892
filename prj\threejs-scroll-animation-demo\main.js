// ===== 导入依赖模块 =====
import './style.css';                    // 导入样式文件
import * as THREE from 'three';          // 导入 Three.js 核心库
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'; // 导入轨道控制器（已注释，用于调试）

// ===== 基础场景设置 =====

// 创建 3D 场景 - 所有 3D 对象的容器
const scene = new THREE.Scene();

// 创建透视相机
// 参数：视野角度(75°), 宽高比, 近裁剪面(0.1), 远裁剪面(1000)
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

// 创建 WebGL 渲染器
// 将 3D 场景渲染到 HTML 中 id 为 'bg' 的 canvas 元素上
const renderer = new THREE.WebGLRenderer({
  canvas: document.querySelector('#bg'),
});

// 设置渲染器像素比，确保在高分辨率屏幕上显示清晰
renderer.setPixelRatio(window.devicePixelRatio);
// 设置渲染器尺寸为窗口大小
renderer.setSize(window.innerWidth, window.innerHeight);
// 设置相机初始位置
camera.position.setZ(30);    // Z轴位置（深度）
camera.position.setX(-3);    // X轴位置（左右）

// 执行初始渲染
renderer.render(scene, camera);

// ===== 创建环形几何体（主要装饰对象）=====

// 创建环形几何体
// 参数：外半径(10), 内半径(3), 径向分段数(16), 管道分段数(100)
const geometry = new THREE.TorusGeometry(10, 3, 16, 100);
// 创建标准材质，支持光照效果
// 颜色：0xff6347 (橙红色)
const material = new THREE.MeshStandardMaterial({ color: 0xff6347 });
// 将几何体和材质组合成网格对象
const torus = new THREE.Mesh(geometry, material);

// 将环形对象添加到场景中
scene.add(torus);

// ===== 光照系统设置 =====

// 创建点光源 - 从一个点向四周发射光线
// 参数：颜色(白色 0xffffff)
const pointLight = new THREE.PointLight(0xffffff);
// 设置点光源位置 (x=5, y=5, z=5)
pointLight.position.set(5, 5, 5);

// 创建环境光 - 均匀照亮场景中的所有对象
// 参数：颜色(白色 0xffffff)，提供基础照明避免过暗
const ambientLight = new THREE.AmbientLight(0xffffff);
// 将两个光源添加到场景中
scene.add(pointLight, ambientLight);

// ===== 调试辅助工具（已注释）=====

// 点光源辅助器 - 可视化点光源位置和范围
// const lightHelper = new THREE.PointLightHelper(pointLight)
// 网格辅助器 - 显示网格线帮助定位
// const gridHelper = new THREE.GridHelper(200, 50);
// scene.add(lightHelper, gridHelper)

// 轨道控制器 - 允许鼠标控制相机视角（调试时使用）
// const controls = new OrbitControls(camera, renderer.domElement);

// ===== 星空效果生成 =====

// 创建单个星星的函数
function addStar() {
  // 创建小球体几何体作为星星
  // 参数：半径(0.25), 宽度分段(24), 高度分段(24)
  const geometry = new THREE.SphereGeometry(0.25, 24, 24);
  // 白色标准材质
  const material = new THREE.MeshStandardMaterial({ color: 0xffffff });
  // 组合成星星网格对象
  const star = new THREE.Mesh(geometry, material);

  // 生成随机位置坐标
  // Array(3).fill() 创建长度为3的数组
  // randFloatSpread(100) 生成 -50 到 50 之间的随机数
  const [x, y, z] = Array(3)
    .fill()
    .map(() => THREE.MathUtils.randFloatSpread(100));

  // 设置星星位置
  star.position.set(x, y, z);
  // 将星星添加到场景中
  scene.add(star);
}

// 创建200个星星填充背景
// Array(200).fill() 创建200个元素的数组，forEach 遍历执行 addStar
Array(200).fill().forEach(addStar);

// ===== 背景纹理设置 =====

// 使用纹理加载器加载太空背景图片
const spaceTexture = new THREE.TextureLoader().load('space.jpg');
// 将纹理设置为场景背景
scene.background = spaceTexture;

// ===== 头像立方体创建 =====

// 加载头像纹理图片
const jeffTexture = new THREE.TextureLoader().load('jeff.png');

// 创建头像立方体
// BoxGeometry(3, 3, 3) - 创建边长为3的立方体
// MeshBasicMaterial - 基础材质，不受光照影响
// map: jeffTexture - 将头像纹理映射到立方体表面
const jeff = new THREE.Mesh(new THREE.BoxGeometry(3, 3, 3), new THREE.MeshBasicMaterial({ map: jeffTexture }));

// 将头像立方体添加到场景中
scene.add(jeff);

// ===== 月球对象创建 =====

// 加载月球表面纹理
const moonTexture = new THREE.TextureLoader().load('moon.jpg');
// 加载法线贴图，用于增强表面细节和光照效果
const normalTexture = new THREE.TextureLoader().load('normal.jpg');

// 创建月球网格对象
const moon = new THREE.Mesh(
  // 球体几何体：半径3，宽度分段32，高度分段32（分段数越高越圆滑）
  new THREE.SphereGeometry(3, 32, 32),
  // 标准材质，支持光照和法线贴图
  new THREE.MeshStandardMaterial({
    map: moonTexture,           // 基础颜色纹理
    normalMap: normalTexture,   // 法线贴图，增强表面凹凸效果
  })
);

// 将月球添加到场景中
scene.add(moon);

// ===== 设置对象初始位置 =====

// 月球位置设置
moon.position.z = 30;      // Z轴位置（远离相机）
moon.position.setX(-10);   // X轴位置（左侧）

// 头像立方体位置设置
jeff.position.z = -5;      // Z轴位置（靠近相机）
jeff.position.x = 2;       // X轴位置（右侧）

// ===== 滚动动画系统 =====

// 相机移动函数 - 根据页面滚动更新3D场景
function moveCamera() {
  // 获取页面滚动距离
  // getBoundingClientRect().top 返回元素顶部相对于视口的距离
  // 向下滚动时值为负数，向上滚动时值为正数
  const t = document.body.getBoundingClientRect().top;

  // 月球旋转动画 - 随滚动持续旋转
  moon.rotation.x += 0.05;    // X轴旋转（上下翻转）
  moon.rotation.y += 0.075;   // Y轴旋转（左右转动）
  moon.rotation.z += 0.05;    // Z轴旋转（倾斜转动）

  // 头像立方体旋转动画 - 较慢的旋转速度
  jeff.rotation.y += 0.01;    // Y轴旋转
  jeff.rotation.z += 0.01;    // Z轴旋转

  // 相机位置变化 - 创建视差滚动效果
  // 乘以负数使滚动方向与移动方向相反，创建自然的滚动感
  camera.position.z = t * -0.01;      // Z轴移动（前后移动）
  camera.position.x = t * -0.0002;    // X轴移动（左右移动，幅度较小）
  camera.rotation.y = t * -0.0002;    // Y轴旋转（左右转向，幅度较小）
}

// 绑定滚动事件监听器
document.body.onscroll = moveCamera;
// 初始调用一次，设置初始状态
moveCamera();

// ===== 主渲染循环 =====

// 动画函数 - 持续更新和渲染场景
function animate() {
  // 请求下一帧动画，创建流畅的60FPS动画循环
  requestAnimationFrame(animate);

  // 环形几何体持续旋转动画
  torus.rotation.x += 0.01;    // X轴旋转
  torus.rotation.y += 0.005;   // Y轴旋转（较慢）
  torus.rotation.z += 0.01;    // Z轴旋转

  // 月球额外的持续旋转（叠加在滚动旋转之上）
  moon.rotation.x += 0.005;

  // 轨道控制器更新（如果启用）
  // controls.update();

  // 渲染场景 - 将3D场景绘制到canvas上
  renderer.render(scene, camera);
}

// 启动动画循环
animate();
