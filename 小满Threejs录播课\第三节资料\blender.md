# blender 教程

设置中文

编辑 -> 偏好设置 -> 语言 -> 简体中文

删除东西 按住小键盘的delete

添加几何体 可以在界面上点击 `添加` 也可以使用快捷键 `shift + a`

## 插件

编辑 -> 偏好设置 -> 插件(打对勾添加即可)

这个插件是python编写的

## 物体模式

- 游标 创建物体起点的位置 快捷键 `shift + 右键
- 移动物体 点击 物体 按快捷键 `g` 或者手动移动 X Y Z 可以拖拽 中间黄点自由移动
- 旋转物体 点击 物体 按快捷键 `r` 或者手动移动 X Y Z 可以拖拽 中间黄点自由移动
- 缩放物体 点击 物体 按快捷键 `s` 或者手动移动 X Y Z 可以拖拽 中间黄点自由移动
- 变换 融合了 移动 旋转 缩放 


# 模型编辑

按 `tab` 进入编辑模式

分别是 点 边 面

shift + 左键 选择多个
ctrl + 左键  框选删除

球体

选中循环面 alt + 左键 坐标上下就是上下 左右就是左右

- 挤出 按E 可以拉出来可以缩回去

# 布尔计算

差集 选中两个物体 按 `ctrl  shift  -` 

交集 选中两个物体 按 `ctrl  shift  +` 

并集 选中两个物体 按 `ctrl  shift  *` 



