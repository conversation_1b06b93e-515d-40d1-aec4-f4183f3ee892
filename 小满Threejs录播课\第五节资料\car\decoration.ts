import * as THREE from 'three';
const decorationParts = {
    wheels: ['wheels', 'hub_rb', 'hub_rf'], // 车轮
    lights: ['pantulans', 'right_rear_light', 'left_front_light', 'indicator_lf', 'indicator_rf', 'light_night', 'indicator_rr', 'indicator_lr', 'left_rear_light', 'revlight_L', 'light_pantulan'], // 灯光
    chrome: ['chrome', 'chrome1', 'chrome2', 'aluminium', 'aluminium2', 'back_chrome_light'], // 镀铬
    interior: ['Seat_Leather_white', 'Leather_white', 'Carpet', 'Carpet_Light', 'texture_Buttons', 'LCDs', 'mirror_inside'], // 内饰
    glass: ['glass'], // 玻璃
    bumpers: ['front_bumper_ok', 'rear_bumper_ok', 'rear_bumper_movsteer'] // 保险杠
};

const decorationPartsMesh: { [key: string]: THREE.Mesh[] } = {};


export const decoration = (scene: THREE.Scene)=> {

    scene.traverse((child)=> {
        if(child instanceof THREE.Mesh) {
            const meshName = child.name.toLowerCase();
            for (const [key,value] of Object.entries(decorationParts)) {
                for (const part of value) {
                    const partName = part.toLowerCase();
                    if(meshName.includes(partName)){
                        if(!decorationPartsMesh[key]) {
                            decorationPartsMesh[key] = [];
                        }
                        decorationPartsMesh[key].push(child);
                        break;
                    }
                }
            }
        }
    })

  
    console.log(decorationPartsMesh)

    const decorationCheckbox = document.querySelectorAll('.decoration-checkbox') as NodeListOf<HTMLInputElement>;
    Array.prototype.forEach.call(decorationCheckbox, (item: HTMLInputElement)=> {
        item.checked = true;
        item.addEventListener('change', ()=> {
            console.log(item.checked,item.id)
            decorationPartsMesh[item.id].forEach(mesh=>{
                //@ts-ignore
                mesh.visible = item.checked;
            })
        })
    })
}