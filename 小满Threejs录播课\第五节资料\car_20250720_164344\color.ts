import * as THREE from 'three';
const colorAry = [
    "rgb(216, 27, 67)", "rgb(142, 36, 170)", "rgb(81, 45, 168)", "rgb(48, 63, 159)", "rgb(30, 136, 229)", "rgb(0, 137, 123)",
    "rgb(67, 160, 71)", "rgb(251, 192, 45)", "rgb(245, 124, 0)", "rgb(230, 74, 25)", "rgb(233, 30, 78)", "rgb(156, 39, 176)",
    "rgb(0, 0, 0)"] // 车身颜色数组 


const genColor = (scene: THREE.Scene) => {
    const colorPicker = document.getElementById('color-picker') as HTMLElement;
    colorAry.forEach(color=>{
        const colorItem = document.createElement('div')
        colorItem.style.backgroundColor = color;
        colorItem.style.width = '40px'
        colorItem.style.height = '40px'
        colorItem.style.marginTop = '10px'
        colorItem.style.cursor = 'pointer'
        colorItem.addEventListener('click',()=>{
            scene.traverse(child=>{
                if(child instanceof THREE.Mesh){
                    if(child.name.startsWith('door_')){
                        child.material.color.set(color)
                    }
                }
            })
        })

        colorPicker.appendChild(colorItem)
    })
}

export default genColor;