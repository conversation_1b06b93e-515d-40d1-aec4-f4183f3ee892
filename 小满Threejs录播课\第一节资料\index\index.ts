import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
//stats 性能监控
import Stats from 'three/addons/libs/stats.module.js';
//创建一个场景
const scene = new THREE.Scene()
const width = 800
const height = 500
//创建一个几何体
//width?: number, height?: number, depth x y z
//立方体 BoxGeometry
//圆柱体   CylinderGeometry
//圆锥体  ConeGeometry
//平面  PlaneGeometry
//原形平面  CircleGeometry
//正四面体 TetrahedronGeometry
//球体 SphereGeometry
const box = new THREE.SphereGeometry(100, 100, 100)




//光源 平行光 聚光灯 环境光 点光源 
const light = new THREE.DirectionalLight(0xffffff, 1)
light.position.set(200, 200, 200)
scene.add(light)

//创建一个材质 
//基础材质 MeshBasicMaterial 什么都没有 光照
//兰伯特材质 MeshLambertMaterial 有光照
//冯氏材质 MeshPhongMaterial 有光照
//卡通材质 MeshToonMaterial 有光照
//法线材质 MeshNormalMaterial 有光照
//深度材质 MeshDepthMaterial 有光照
//精灵材质 SpriteMaterial 有光照
const material = new THREE.MeshLambertMaterial({
    // emissiveIntensity: 1,
    // emissive: 0x000000,
    color: 0x00FFFF,
    wireframe: true, //线框
    // specular: 0x1188ff, //高光颜色
    // shininess: 100, //光泽度
})
//图形学
//兰伯特漫反射模型 常用于粗糙表面 四面八方扩散
//冯氏光照模型-镜面高光技术 折射 冯氏光照模型是基于兰伯特漫反射模型实现的
//所以说冯氏光照模型可以用漫反射的参数

//创建网格
const mesh = new THREE.Mesh(box, material)

//将网格添加到场景中
scene.add(mesh)
//辅助坐标轴
const axesHelper = new THREE.AxesHelper(150);
scene.add(axesHelper)

//创建一个相机
//第一个参数角度 这个值越大物体越小
//宽度比例1.6
//近剪裁面1 
//远剪裁面3000
const camera = new THREE.PerspectiveCamera(45, width / height)
camera.position.set(200,200,200)// x y z
camera.lookAt(mesh.position)
//将相机添加到场景中
scene.add(camera)

//创建渲染器
const renderer = new THREE.WebGLRenderer({
    canvas: document.querySelector('#canvas') as HTMLCanvasElement,
    antialias: true, //抗锯齿
})
renderer.setSize(width, height) //设置渲染器大小
//renderer.render(scene, camera) //渲染

const stats = new Stats()
document.body.appendChild(stats.dom)

//创建控制器
const controls = new OrbitControls(camera, renderer.domElement)


const animate = () => {
    requestAnimationFrame(animate) //RAF 60帧 代替计时器
    renderer.render(scene, camera)
    mesh.rotation.x += 0.01
    mesh.rotation.y += 0.01
    mesh.material.color.set(0x0000ff)
    controls.update()
    stats.update()
}
animate()


//留个作业
//画一个多啦A梦