# Three.js 滚动动画演示项目
- 项目技术架构图
```mermaid
graph LR
    A[HTML页面结构] --> B[Canvas画布]
    A --> C[内容区域]
    
    B --> D[Three.js场景]
    D --> E[3D对象]
    D --> F[光照系统]
    D --> G[相机系统]
    
    E --> H[环形几何体]
    E --> I[星空效果]
    E --> J[头像立方体]
    E --> K[月球]
    
    C --> L[CSS Grid布局]
    L --> M[Header区域]
    L --> N[Section区域]
    L --> O[Blockquote区域]
    
    P[滚动事件] --> Q[moveCamera函数]
    Q --> R[相机位置更新]
    Q --> S[对象旋转更新]
    
    T[动画循环] --> U[requestAnimationFrame]
    U --> V[持续旋转动画]
    U --> W[场景渲染]

```
## 项目概述

这是一个基于 Three.js 的交互式 3D 网页项目，展示了如何将 3D 图形与页面滚动动画相结合，创建沉浸式的用户体验。项目以个人网站的形式呈现，包含了多个 3D 对象和动态效果。

## 技术栈

- **Three.js** (v0.128.0) - 3D 图形库
- **Vite** (v2.3.0) - 构建工具和开发服务器
- **HTML5 Canvas** - 3D 渲染画布
- **CSS3** - 样式和布局
- **JavaScript ES6+** - 核心逻辑

## 项目结构

```
threejs-scroll-animation-demo/
├── index.html          # 主页面结构
├── main.js             # Three.js 核心逻辑
├── style.css           # 样式文件
├── package.json        # 项目配置
├── favicon.svg         # 网站图标
├── jeff.png            # 头像纹理
├── moon.jpg            # 月球纹理
├── normal.jpg          # 法线贴图
├── space.jpg           # 背景纹理
└── node_modules/       # 依赖包
```

## 核心功能分析

### 1. 3D 场景设置 (main.js)

#### 基础场景配置
```javascript
// 创建场景、相机和渲染器
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ canvas: document.querySelector('#bg') });
```

#### 3D 对象创建

**1. 环形几何体 (Torus)**
- 几何体：`TorusGeometry(10, 3, 16, 100)`
- 材质：`MeshStandardMaterial` 橙红色 (#ff6347)
- 持续旋转动画

**2. 星空效果**
- 200个随机分布的小球体
- 使用 `THREE.MathUtils.randFloatSpread(100)` 随机定位
- 白色发光材质

**3. 头像立方体 (Jeff)**
- 立方体几何体 `BoxGeometry(3, 3, 3)`
- 使用 jeff.png 作为纹理贴图
- 响应滚动的旋转动画

**4. 月球**
- 球体几何体 `SphereGeometry(3, 32, 32)`
- 使用月球纹理和法线贴图
- 增强的表面细节效果

### 2. 光照系统

```javascript
// 点光源 - 提供主要照明
const pointLight = new THREE.PointLight(0xffffff);
pointLight.position.set(5, 5, 5);

// 环境光 - 提供整体照明
const ambientLight = new THREE.AmbientLight(0xffffff);
```

### 3. 滚动动画核心机制

#### 滚动监听函数
```javascript
function moveCamera() {
  const t = document.body.getBoundingClientRect().top;

  // 对象旋转动画
  moon.rotation.x += 0.05;
  moon.rotation.y += 0.075;
  moon.rotation.z += 0.05;
  jeff.rotation.y += 0.01;
  jeff.rotation.z += 0.01;

  // 相机位置变化
  camera.position.z = t * -0.01;
  camera.position.x = t * -0.0002;
  camera.rotation.y = t * -0.0002;
}
```

**动画原理：**
- 使用 `getBoundingClientRect().top` 获取滚动距离
- 将滚动值转换为 3D 对象的位置和旋转变化
- 创建视差效果和深度感

### 4. 渲染循环

```javascript
function animate() {
  requestAnimationFrame(animate);

  // 持续旋转动画
  torus.rotation.x += 0.01;
  torus.rotation.y += 0.005;
  torus.rotation.z += 0.01;
  moon.rotation.x += 0.005;

  renderer.render(scene, camera);
}
```

## 样式设计分析 (style.css)

### 1. 布局系统
- 使用 CSS Grid 布局 (`grid-template-columns: repeat(12, 1fr)`)
- 固定定位的 Canvas 背景
- 响应式网格系统

### 2. 视觉设计
- **颜色方案：** 深色背景 `rgba(15, 15, 15, 0.95)` 与白色文字
- **字体：** brandon-grotesque 和 elevon 字体族
- **间距：** 统一使用 `--spacing: 350px` CSS 变量

### 3. 内容区域样式
```css
header {
  grid-column: 2 / span 5;  /* 占据5列 */
  background: var(--dark-bg);
  font-size: 2.5rem;
}

section {
  grid-column: 2 / 8;       /* 占据6列 */
  background: var(--dark-bg);
  font-size: 1.25rem;
}

blockquote {
  grid-column: 2 / span 9;  /* 占据9列 */
  color: black;
  background-color: white;
  font-size: 4rem;
}
```

## 页面结构分析 (index.html)

### 内容组织
1. **Header** - 个人介绍和欢迎信息
2. **引言** - 个人理念展示
3. **宣言部分** - 详细的个人描述
4. **项目和成就** - 使用 `.light` 类的特殊样式
5. **工作经历** - 使用 `.left` 类右对齐显示
6. **结语引言** - 感谢信息

### 关键元素
- `<canvas id="bg">` - Three.js 渲染目标
- `<main>` - 主要内容容器，使用网格布局
- 多个 `<section>` 和 `<blockquote>` 创建滚动内容

## 运行说明

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览构建结果
```bash
npm run serve
```

## 技术特点

### 1. 性能优化
- 使用 `requestAnimationFrame` 确保流畅动画
- 合理的几何体复杂度设置
- 高效的纹理加载

### 2. 用户体验
- 平滑的滚动视差效果
- 响应式设计适配不同屏幕
- 沉浸式的 3D 背景

### 3. 代码组织
- 模块化的 JavaScript 结构
- 清晰的功能分离
- 易于维护和扩展

## 学习要点

### Three.js 核心概念
1. **场景 (Scene)** - 3D 世界的容器
2. **相机 (Camera)** - 观察视角
3. **渲染器 (Renderer)** - 将 3D 场景渲染到 2D 画布
4. **几何体 (Geometry)** - 3D 对象的形状
5. **材质 (Material)** - 3D 对象的外观
6. **网格 (Mesh)** - 几何体和材质的组合

### 动画技术
1. **渲染循环** - 使用 `requestAnimationFrame`
2. **滚动监听** - 将页面滚动转换为 3D 动画
3. **视差效果** - 不同对象的不同移动速度
4. **纹理映射** - 为 3D 对象添加真实感

### Web 开发集成
1. **Canvas 与 HTML 结合** - 3D 背景与传统网页内容
2. **CSS Grid 布局** - 现代网页布局技术
3. **模块化开发** - ES6 模块和现代构建工具

## 扩展建议

1. **添加更多交互** - 鼠标悬停效果、点击事件
2. **性能监控** - 添加 FPS 显示和性能统计
3. **移动端优化** - 触摸手势支持和性能调优
4. **音效集成** - 添加背景音乐和交互音效
5. **粒子系统** - 更复杂的视觉效果

## 原始教程资源

- 观看 [完整教程](https://youtu.be/Q7AOvWpIVHU) 在 YouTube
- [Three.js 滚动动画](https://fireship.io/snippets/threejs-scrollbar-animation) 代码片段

---

## Three.js 初学者完整教程

### 什么是 Three.js？

Three.js 是一个基于 WebGL 的 JavaScript 3D 图形库，它简化了在网页中创建和显示 3D 图形的过程。想象一下，如果 WebGL 是汇编语言，那么 Three.js 就是高级编程语言 - 它让复杂的 3D 编程变得简单易懂。

### Three.js 的核心概念

在开始学习之前，我们需要理解 Three.js 的三个核心组件，就像拍电影一样：

1. **场景 (Scene)** - 电影的拍摄现场，所有演员和道具都在这里
2. **相机 (Camera)** - 摄像机，决定观众看到什么
3. **渲染器 (Renderer)** - 导演，把场景通过相机拍摄并呈现给观众

### 第一步：创建基础场景

让我们从项目代码开始，逐步理解每个概念：

```javascript
// 创建场景 - 这是所有3D对象的容器
const scene = new THREE.Scene();
```

**场景就像一个空的舞台**，我们将在这里放置所有的3D对象。

```javascript
// 创建相机 - 决定我们如何观察3D世界
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
```

**透视相机的参数解释：**
- `75` - 视野角度（FOV），就像人眼的视野范围，75度是比较自然的视角
- `window.innerWidth / window.innerHeight` - 宽高比，保持画面不变形
- `0.1` - 近裁剪面，相机能看到的最近距离
- `1000` - 远裁剪面，相机能看到的最远距离

```javascript
// 创建渲染器 - 负责将3D场景绘制到网页上
const renderer = new THREE.WebGLRenderer({
  canvas: document.querySelector('#bg'),
});
```

**渲染器的作用**就像打印机，把3D场景"打印"到HTML的canvas元素上。

### 第二步：理解几何体、材质和网格

Three.js 中的3D对象由三部分组成：

#### 1. 几何体 (Geometry) - 形状

```javascript
// 创建环形几何体 - 定义对象的形状
const geometry = new THREE.TorusGeometry(10, 3, 16, 100);
```

**几何体就像雕塑的基本形状**：
- `10` - 环的外半径
- `3` - 环的内半径（管子的粗细）
- `16` - 径向分段数（环绕方向的分段）
- `100` - 管道分段数（管子方向的分段）

分段数越高，形状越圆滑，但性能消耗也越大。

#### 2. 材质 (Material) - 外观

```javascript
// 创建材质 - 定义对象的外观
const material = new THREE.MeshStandardMaterial({ color: 0xff6347 });
```

**材质就像物体的皮肤**，决定了：
- 颜色
- 反光程度
- 透明度
- 纹理等

`MeshStandardMaterial` 是标准材质，支持光照效果，让物体看起来更真实。

#### 3. 网格 (Mesh) - 组合体

```javascript
// 将几何体和材质组合成网格
const torus = new THREE.Mesh(geometry, material);
// 添加到场景中
scene.add(torus);
```

**网格就是几何体+材质的组合**，这样我们就有了一个完整的3D对象。

### 第三步：光照系统 - 让物体可见

没有光，我们什么都看不见。Three.js 提供了多种光源：

#### 点光源 (Point Light)

```javascript
// 创建点光源 - 像灯泡一样向四周发光
const pointLight = new THREE.PointLight(0xffffff);
pointLight.position.set(5, 5, 5);
```

**点光源特点：**
- 从一个点向四周发射光线
- 有位置，光线强度随距离衰减
- 就像现实中的灯泡

#### 环境光 (Ambient Light)

```javascript
// 创建环境光 - 均匀照亮所有物体
const ambientLight = new THREE.AmbientLight(0xffffff);
```

**环境光特点：**
- 没有方向，均匀照亮所有物体
- 避免物体过暗，提供基础照明
- 就像阴天的自然光

### 第四步：纹理贴图 - 让物体更真实

```javascript
// 加载纹理图片
const moonTexture = new THREE.TextureLoader().load('moon.jpg');
const normalTexture = new THREE.TextureLoader().load('normal.jpg');

// 使用纹理的材质
const moon = new THREE.Mesh(
  new THREE.SphereGeometry(3, 32, 32),
  new THREE.MeshStandardMaterial({
    map: moonTexture,           // 基础颜色纹理
    normalMap: normalTexture,   // 法线贴图，增强表面细节
  })
);
```

**纹理类型：**
- `map` - 基础颜色纹理，就像给物体贴上彩色贴纸
- `normalMap` - 法线贴图，让平面看起来有凹凸感，但不改变实际形状

### 第五步：动画系统 - 让场景动起来

#### 基础旋转动画

```javascript
function animate() {
  requestAnimationFrame(animate);  // 创建动画循环

  // 让环形几何体旋转
  torus.rotation.x += 0.01;
  torus.rotation.y += 0.005;
  torus.rotation.z += 0.01;

  renderer.render(scene, camera);  // 重新渲染场景
}
animate();  // 启动动画
```

**动画原理：**
1. `requestAnimationFrame` 创建60FPS的动画循环
2. 每帧微调对象的旋转角度
3. 重新渲染场景
4. 视觉上就形成了流畅的旋转动画

#### 交互动画 - 滚动控制

```javascript
function moveCamera() {
  // 获取页面滚动距离
  const t = document.body.getBoundingClientRect().top;

  // 根据滚动距离调整相机位置
  camera.position.z = t * -0.01;
  camera.position.x = t * -0.0002;
  camera.rotation.y = t * -0.0002;
}

// 监听滚动事件
document.body.onscroll = moveCamera;
```

**交互动画原理：**
1. 监听页面滚动事件
2. 将滚动距离转换为3D空间的位置变化
3. 创建视差效果，让用户感觉在3D空间中移动

### 第六步：坐标系统理解

Three.js 使用右手坐标系：
- **X轴** - 左右方向（正值向右）
- **Y轴** - 上下方向（正值向上）
- **Z轴** - 前后方向（正值向外，朝向观察者）

```javascript
// 设置对象位置
moon.position.z = 30;      // 远离相机
moon.position.setX(-10);   // 向左移动
jeff.position.z = -5;      // 靠近相机
jeff.position.x = 2;       // 向右移动
```

### 第七步：常用几何体类型

项目中使用了多种几何体，让我们了解它们的特点：

#### 1. 环形几何体 (TorusGeometry)
```javascript
new THREE.TorusGeometry(10, 3, 16, 100)
```
- 适合做装饰性对象
- 参数：外半径、内半径、径向分段、管道分段

#### 2. 球体几何体 (SphereGeometry)
```javascript
new THREE.SphereGeometry(3, 32, 32)
```
- 适合做星球、球体对象
- 参数：半径、宽度分段、高度分段

#### 3. 立方体几何体 (BoxGeometry)
```javascript
new THREE.BoxGeometry(3, 3, 3)
```
- 最简单的几何体
- 参数：宽度、高度、深度

### 第八步：材质类型详解

#### 1. 基础材质 (MeshBasicMaterial)
```javascript
new THREE.MeshBasicMaterial({ map: jeffTexture })
```
- **特点：** 不受光照影响，始终保持原色
- **用途：** 适合UI元素、发光物体
- **性能：** 最快，消耗最少

#### 2. 标准材质 (MeshStandardMaterial)
```javascript
new THREE.MeshStandardMaterial({
  map: moonTexture,
  normalMap: normalTexture,
})
```
- **特点：** 支持光照，更真实
- **用途：** 大多数3D对象的首选
- **性能：** 中等消耗

### 第九步：性能优化技巧

#### 1. 合理控制几何体复杂度
```javascript
// 低精度版本（性能好）
new THREE.SphereGeometry(3, 16, 16)

// 高精度版本（质量好）
new THREE.SphereGeometry(3, 32, 32)
```

#### 2. 批量创建对象
```javascript
// 高效的星空创建方式
function addStar() {
  const geometry = new THREE.SphereGeometry(0.25, 24, 24);
  const material = new THREE.MeshStandardMaterial({ color: 0xffffff });
  const star = new THREE.Mesh(geometry, material);

  // 随机位置
  const [x, y, z] = Array(3)
    .fill()
    .map(() => THREE.MathUtils.randFloatSpread(100));

  star.position.set(x, y, z);
  scene.add(star);
}

// 创建200个星星
Array(200).fill().forEach(addStar);
```

### 第十步：调试技巧

#### 1. 使用辅助工具
```javascript
// 显示光源位置（调试时取消注释）
const lightHelper = new THREE.PointLightHelper(pointLight)
scene.add(lightHelper)

// 显示网格线
const gridHelper = new THREE.GridHelper(200, 50);
scene.add(gridHelper)
```

#### 2. 轨道控制器
```javascript
// 允许鼠标控制相机（调试时使用）
const controls = new OrbitControls(camera, renderer.domElement);
```

### 常见问题解答

#### Q1: 为什么我的3D对象是黑色的？
**A:** 可能是缺少光照。确保添加了光源：
```javascript
const ambientLight = new THREE.AmbientLight(0xffffff);
scene.add(ambientLight);
```

#### Q2: 为什么动画不流畅？
**A:** 检查以下几点：
- 使用 `requestAnimationFrame` 而不是 `setInterval`
- 降低几何体的分段数
- 减少场景中的对象数量

#### Q3: 如何让对象响应鼠标点击？
**A:** 使用射线投射 (Raycasting)：
```javascript
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

function onMouseClick(event) {
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(scene.children);

  if (intersects.length > 0) {
    // 点击到了对象
    console.log('点击了:', intersects[0].object);
  }
}

window.addEventListener('click', onMouseClick);
```

### 学习建议

#### 初学者路径：
1. **第1周：** 掌握基础概念（场景、相机、渲染器）
2. **第2周：** 学习几何体和材质
3. **第3周：** 理解光照系统
4. **第4周：** 掌握基础动画
5. **第5周：** 学习纹理和贴图
6. **第6周：** 实现用户交互

#### 实践项目建议：
1. **旋转的立方体** - 掌握基础概念
2. **太阳系模拟** - 学习复杂动画
3. **3D产品展示** - 掌握光照和材质
4. **交互式场景** - 学习用户交互

---

这个项目是学习 Three.js 和现代 Web 3D 开发的优秀示例，展示了如何将 3D 图形无缝集成到传统网页中，创造独特的用户体验。通过分析这个项目，您可以掌握 3D Web 开发的核心技术和最佳实践。